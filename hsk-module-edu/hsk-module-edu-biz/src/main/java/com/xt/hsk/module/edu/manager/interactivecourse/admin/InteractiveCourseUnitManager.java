package com.xt.hsk.module.edu.manager.interactivecourse.admin;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.INTERACTIVE_COURSE_NOT_EXISTS;

import cn.hutool.core.collection.CollUtil;
import com.mzt.logapi.context.LogRecordContext;
import com.xt.hsk.framework.common.constants.RedisKeyPrefix;
import com.xt.hsk.framework.common.enums.CommonStatusEnum;
import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.framework.common.enums.WordKindEnum;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.BinaryUtils;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.content.CourseVideoLinkVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.content.CoursewareInfoVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.content.VocabularyInfoVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseUnitPageReqVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseUnitRespVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseUnitRespVO.QuestionInfoVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseUnitSaveReqVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseUnitSaveReqVO.SpecialPracticeInfoVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseUnitSaveReqVO.VideoInfoVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.UnitQuestionInfoVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.UnitSpecialPracticeInfoVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.UnitVocabularyInfoVO;
import com.xt.hsk.module.edu.convert.interactivecourse.InteractiveCourseUnitConvert;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.InteractiveCourseCoursewareDO;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.InteractiveCourseDO;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.InteractiveCourseUnitDO;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.InteractiveCourseUnitResourceRelDO;
import com.xt.hsk.module.edu.dal.dataobject.question.QuestionDO;
import com.xt.hsk.module.edu.dal.dataobject.textbook.TextbookDO;
import com.xt.hsk.module.edu.dal.dataobject.word.WordDO;
import com.xt.hsk.module.edu.dal.dataobject.word.WordMeaningDO;
import com.xt.hsk.module.edu.enums.ErrorCodeConstants;
import com.xt.hsk.module.edu.enums.interactivecourse.InteractiveCourseUnitIconUrlEnum;
import com.xt.hsk.module.edu.enums.interactivecourse.UnitQuestionSourceTypeEnum;
import com.xt.hsk.module.edu.enums.interactivecourse.UnitResourceTypeEnum;
import com.xt.hsk.module.edu.enums.interactivecourse.VideoTypeEnum;
import com.xt.hsk.module.edu.service.interactivecourse.InteractiveCourseCoursewareService;
import com.xt.hsk.module.edu.service.interactivecourse.InteractiveCourseService;
import com.xt.hsk.module.edu.service.interactivecourse.InteractiveCourseUnitResourceRelService;
import com.xt.hsk.module.edu.service.interactivecourse.InteractiveCourseUnitService;
import com.xt.hsk.module.edu.service.question.QuestionService;
import com.xt.hsk.module.edu.service.textbook.TextbookService;
import com.xt.hsk.module.edu.service.word.WordMeaningService;
import com.xt.hsk.module.edu.service.word.WordService;
import com.xt.hsk.module.game.api.SpecialExerciseApi;
import com.xt.hsk.module.game.api.dto.SpecialExercisePageRespDTO;
import com.xt.hsk.module.game.enums.specialexercise.SpecialExerciseTypeEnum;
import com.xt.hsk.module.infra.api.config.ConfigApi;
import com.xt.hsk.module.thirdparty.api.video.VideoApi;
import com.xt.hsk.module.thirdparty.api.video.dto.VideoRespDTO;
import com.xt.hsk.module.thirdparty.enums.VideoQualityEnum;
import com.xt.hsk.module.thirdparty.enums.VideoSourceEnum;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 互动课单元管理器
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Slf4j
@Component
public class InteractiveCourseUnitManager {

    /**
     * 视频带课件
     */
    private static final int VIDEO_TYPE_WITH_COURSEWARE = 1;
    /**
     * 视频带生词
     */
    private static final int VIDEO_TYPE_WITH_VOCABULARY = 2;

    /**
     * 视频尺寸比例常量
     * 9:16
     */
    private static final int ASPECT_RATIO_9_16 = 1;
    /**
     * 视频尺寸比例常量
     * 16:9
     */
    private static final int ASPECT_RATIO_16_9 = 2;



    @Resource
    private InteractiveCourseUnitService interactiveCourseUnitService;

    @Resource
    private InteractiveCourseService interactiveCourseService;

    @Resource
    private WordService wordService;

    @Resource
    private InteractiveCourseCoursewareService coursewareService;

    @Resource
    private InteractiveCourseUnitResourceRelService unitResourceRelService;

    @Resource
    private QuestionService questionService;

    @Resource
    private SpecialExerciseApi specialExerciseApi;

    @Resource
    private TextbookService textbookService;

    @Resource
    private WordMeaningService wordMeaningService;

    @Resource
    private ConfigApi configApi;

    @Resource
    private VideoApi videoApi;

    /**
     * 拆分字符
     */
    private static final String SPLIT_CHAR = ";";

    /**
     * 删除互动课单元
     *
     * @param id 互动课单元ID
     */
    @CacheEvict(value = RedisKeyPrefix.INTERACTIVE_COURSE_DISPLAY_COUNT, allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void deleteInteractiveCourseUnit(Long id) {
        // 校验存在并获取单元信息
        InteractiveCourseUnitDO unit = validateInteractiveCourseUnitExists(id);

        // 获取课程信息
        InteractiveCourseDO course = interactiveCourseService.getById(unit.getCourseId());

        // 设置日志上下文变量
        LogRecordContext.putVariable("unit", unit);
        LogRecordContext.putVariable("courseName", course != null ? course.getCourseNameCn() : "未知课程");

        // 删除
        interactiveCourseUnitService.removeById(id);
        // 清理旧的资源关联
        clearOldResources(id);
    }

    /**
     * 校验互动课单元是否存在
     *
     * @param id 互动课单元ID
     * @return 互动课单元DO
     */
    private InteractiveCourseUnitDO validateInteractiveCourseUnitExists(Long id) {
        InteractiveCourseUnitDO unitDO = interactiveCourseUnitService.getById(id);
        if (unitDO == null) {
            throw exception(ErrorCodeConstants.INTERACTIVE_COURSE_UNIT_NOT_EXISTS);
        }
        return unitDO;
    }

    /**
     * 获取互动课单元分页
     */
    public PageResult<InteractiveCourseUnitRespVO> getInteractiveCourseUnitVoPage(
        InteractiveCourseUnitPageReqVO pageReqVO) {
        PageResult<InteractiveCourseUnitDO> pageResult = interactiveCourseUnitService.getInteractiveCourseUnitPage(
            pageReqVO);
        List<InteractiveCourseUnitRespVO> respVOList = InteractiveCourseUnitConvert.INSTANCE.doListToRespVOList(
            pageResult.getList());

        Map<Long, String> unitContentType = interactiveCourseUnitService.getUnitContentType(
            respVOList.stream().map(InteractiveCourseUnitRespVO::getId).toList());
        respVOList.forEach(respVO -> respVO.setUnitQuestionTypeDesc(unitContentType.get(respVO.getId())));
        return new PageResult<>(respVOList, pageResult.getTotal());
    }

    /**
     * 更新状态（隐藏或展示）
     *
     * @param id 互动课单元ID
     */
    @CacheEvict(value = RedisKeyPrefix.INTERACTIVE_COURSE_DISPLAY_COUNT,allEntries = true)
    public void updateStatus(Long id) {
        // 校验存在
        InteractiveCourseUnitDO unitDO = validateInteractiveCourseUnitExists(id);
        unitDO.setDisplayStatus(!unitDO.getDisplayStatus());
        interactiveCourseUnitService.updateById(unitDO);

        // 设置日志上下文变量
        String statusText = Boolean.TRUE.equals(unitDO.getDisplayStatus()) ? "显示" : "隐藏";
        LogRecordContext.putVariable("statusText", statusText);
        LogRecordContext.putVariable("unit", unitDO);
    }

    /**
     * 修改排序
     * <p>
     * 序号在同一课程ID下不重复
     * 采用区间调整法，保持序号连续性
     * </p>
     */
    public void updateSort(Long id, Integer newSort) {
        // 校验存在
        InteractiveCourseUnitDO unit = validateInteractiveCourseUnitExists(id);
        Integer oldSort = unit.getSort();

        // 设置日志上下文变量
        LogRecordContext.putVariable("oldSort", oldSort);
        LogRecordContext.putVariable("unit", unit);
        // 不需要调整
        if (Objects.equals(oldSort, newSort)) {
            return;
        }

        // 在同一课程下调整序号
        if (newSort < oldSort) {
            // 向前移动：将[newSort, oldSort-1]范围内的单元序号+1
            interactiveCourseUnitService.lambdaUpdate()
                .setSql("sort = sort + 1")
                .eq(InteractiveCourseUnitDO::getCourseId, unit.getCourseId())
                .between(InteractiveCourseUnitDO::getSort, newSort, oldSort - 1)
                .update();
        } else {
            // 向后移动：将[oldSort+1, newSort]范围内的单元序号-1
            interactiveCourseUnitService.lambdaUpdate()
                .setSql("sort = sort - 1")
                .eq(InteractiveCourseUnitDO::getCourseId, unit.getCourseId())
                .between(InteractiveCourseUnitDO::getSort, oldSort + 1, newSort)
                .update();
        }

        // 设置该单元的新序号
        unit.setSort(newSort);
        unit.setUpdateTime(LocalDateTime.now());
        interactiveCourseUnitService.updateById(unit);

        LogRecordContext.putVariable("unitDO", unit);
    }

    /**
     * 创建互动课单元（新） 新增时自动生成序号，序号不重复，最新创建的单元序号默认为当前最大序号+1（以当前课程ID维度）
     *
     * @param createReqVO 创建互动课单元请求VO
     * @return 互动课单元ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long createInteractiveCourseUnitNew(InteractiveCourseUnitSaveReqVO createReqVO) {
        // 校验课程存在
        validateCourseExists(createReqVO.getCourseId());

        // 校验参数
        validateUnitParams(createReqVO);

        // 创建互动课单元
        InteractiveCourseUnitDO unitDO = InteractiveCourseUnitConvert.INSTANCE.doToDataObject(
            createReqVO);
        // 设置初始资源版本号
        unitDO.setResourceVersion(1);
        interactiveCourseUnitService.save(unitDO);
        Long unitId = unitDO.getId();

        // 点播视频ID(ID来自第三方)
        if (createReqVO.getQuestionSource().equals(UnitQuestionSourceTypeEnum.VIDEO.getCode())) {
            videoApi.assetIdIsExist(createReqVO.getVideoId(),
                VideoSourceEnum.INTERACTIVE_COURSE.getCode());
            unitDO.setVideoId(createReqVO.getVideoId());
        }

        // 设置排序号
        setUnitSort(unitDO);

        // 处理关联资源
        processUnitResources(unitId, createReqVO,unitDO);

        // 更新互动课单元
        interactiveCourseUnitService.updateById(unitDO);

        // 获取课程信息
        InteractiveCourseDO course = interactiveCourseService.getById(createReqVO.getCourseId());

        // 设置日志上下文变量
        LogRecordContext.putVariable("unitId", unitId);
        LogRecordContext.putVariable("unit", unitDO);
        LogRecordContext.putVariable("courseName", course != null ? course.getCourseNameCn() : "未知课程");

        return unitId;
    }

    /**
     * 校验课程存在
     *
     * @param courseId 课程ID
     */
    private void validateCourseExists(Long courseId) {
        InteractiveCourseDO course = interactiveCourseService.getById(courseId);
        if (course == null) {
            throw exception(INTERACTIVE_COURSE_NOT_EXISTS);
        }
    }

    /**
     * 设置互动课单元排序
     *
     * @param unitDO 互动课单元DO
     */
    private void setUnitSort(InteractiveCourseUnitDO unitDO) {
        InteractiveCourseUnitDO maxSortUnit = interactiveCourseUnitService.lambdaQuery()
            .eq(InteractiveCourseUnitDO::getCourseId, unitDO.getCourseId())
            .orderByDesc(InteractiveCourseUnitDO::getSort)
            .last("LIMIT 1")
            .one();
        unitDO.setSort(maxSortUnit == null ? 1 : maxSortUnit.getSort() + 1);
    }

    /**
     * 根据配置参数key获取配置参数
     *
     * @param key key值
     * @return 配置参数的 val
     */
    private String getConfigByKey(String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }
        return configApi.getConfigValueByKey(key);
    }

    /**
     * 处理单元关联资源
     *
     * @param unitId 单元ID
     * @param reqVO  请求VO
     * @param unitDO 互动课单元DO
     */
    private void processUnitResources(Long unitId, InteractiveCourseUnitSaveReqVO reqVO,
        InteractiveCourseUnitDO unitDO) {


        if (UnitQuestionSourceTypeEnum.VIDEO.getCode().equals(reqVO.getQuestionSource())
            && reqVO.getVideoInfo() != null) {

            unitDO.setVideoType(reqVO.getVideoInfo().getVideoType());
            unitDO.setAspectRatio(reqVO.getVideoInfo().getAspectRatio());
            unitDO.setVideoNameCn(reqVO.getVideoInfo().getVideoNameCn());
            unitDO.setVideoNameEn(reqVO.getVideoInfo().getVideoNameEn());
            unitDO.setVideoNameOt(reqVO.getVideoInfo().getVideoNameOt());
            // 设置icon地址
            unitDO.setCoverUrl(getConfigByKey(InteractiveCourseUnitIconUrlEnum.VIDEO.getCode()));

            // 处理视频相关资源
            if (VIDEO_TYPE_WITH_COURSEWARE == reqVO.getVideoInfo().getVideoType()
                && CollUtil.isNotEmpty(reqVO.getCoursewareList())) {

                // 处理课件数据
                saveCoursewareResources(unitId, reqVO.getCoursewareList());
                // 设置icon地址
                unitDO.setCoverUrl(getConfigByKey(
                    InteractiveCourseUnitIconUrlEnum.VIDEO_WITH_COURSEWARE.getCode()));
            } else if (VIDEO_TYPE_WITH_VOCABULARY == reqVO.getVideoInfo().getVideoType()
                && CollUtil.isNotEmpty(reqVO.getVocabularyList())) {

                // 设置生词数据
                saveVocabularyResources(unitId, reqVO.getVocabularyList());
                // 设置icon地址
                unitDO.setCoverUrl(getConfigByKey(
                    InteractiveCourseUnitIconUrlEnum.VIDEO_WITH_VOCABULARY.getCode()));
                }

        } else if (UnitQuestionSourceTypeEnum.SPECIAL_PRACTICE.getCode().equals(reqVO.getQuestionSource())) {
            // 处理专项练习资源
            saveSpecialPracticeResource(unitId, reqVO.getSpecialPracticeInfo(),unitDO);
        } else if (UnitQuestionSourceTypeEnum.QUESTION.getCode().equals(reqVO.getQuestionSource())) {
            // 处理真题练习资源
            saveRealExamResource(unitId, reqVO.getRealExamInfo());

            // 设置icon地址
            unitDO.setCoverUrl(
                getConfigByKey(InteractiveCourseUnitIconUrlEnum.REAL_EXERCISE.getCode()));
        }

    }

    /**
     * 校验单元参数
     */
    private void validateUnitParams(InteractiveCourseUnitSaveReqVO reqVO) {
        Integer questionSource = reqVO.getQuestionSource();

        if (UnitQuestionSourceTypeEnum.VIDEO.getCode().equals(questionSource)) {
            validateVideoParams(reqVO);
        } else if (UnitQuestionSourceTypeEnum.SPECIAL_PRACTICE.getCode().equals(questionSource)) {
            validateSpecialPracticeParams(reqVO);
        } else if (UnitQuestionSourceTypeEnum.QUESTION.getCode().equals(questionSource)) {
            validateRealExamParams(reqVO);
        }
    }

    /**
     * 校验视频相关参数
     */
    private void validateVideoParams(InteractiveCourseUnitSaveReqVO reqVO) {
        VideoInfoVO videoInfo = reqVO.getVideoInfo();
        if (videoInfo == null) {
            throw exception(ErrorCodeConstants.INVALID_REQUEST_PARAM_ERROR,
                "视频类型的单元必须提供视频信息");
        }

        // 校验视频类型
        if (videoInfo.getVideoType() != null && VIDEO_TYPE_WITH_COURSEWARE == videoInfo.getVideoType()) {
            validateVideoWithCoursewareParams(videoInfo, reqVO);
        } else if (videoInfo.getVideoType() != null && VIDEO_TYPE_WITH_VOCABULARY == videoInfo.getVideoType()) {
            validateVideoWithVocabularyParams(videoInfo, reqVO);
        } else {
            throw exception(ErrorCodeConstants.INVALID_REQUEST_PARAM_ERROR, "无效的视频类型");
        }
    }

    /**
     * 校验带课件视频参数
     */
    private void validateVideoWithCoursewareParams(VideoInfoVO videoInfo,
        InteractiveCourseUnitSaveReqVO reqVO) {
        // 校验视频尺寸比例
        if (videoInfo.getAspectRatio() != ASPECT_RATIO_9_16
            && videoInfo.getAspectRatio() != ASPECT_RATIO_16_9) {
            throw exception(ErrorCodeConstants.INVALID_REQUEST_PARAM_ERROR,
                "视频带课件时，视频尺寸比例只能是1(9:16)或2(16:9)");
        }

        // 校验不能包含生词信息
        if (CollUtil.isNotEmpty(reqVO.getVocabularyList())) {
            throw exception(ErrorCodeConstants.INVALID_REQUEST_PARAM_ERROR,
                "视频带课件时，不能包含生词信息");
        }
    }

    /**
     * 校验带生词视频参数
     */
    private void validateVideoWithVocabularyParams(VideoInfoVO videoInfo,
        InteractiveCourseUnitSaveReqVO reqVO) {
        // 校验视频尺寸比例
        if (videoInfo.getAspectRatio() != ASPECT_RATIO_16_9) {
            throw exception(ErrorCodeConstants.INVALID_REQUEST_PARAM_ERROR,
                "视频带生词时，视频尺寸比例只能是2(16:9)");
        }

        // 校验不能包含课件信息
        if (CollUtil.isNotEmpty(reqVO.getCoursewareList())) {
            throw exception(ErrorCodeConstants.INVALID_REQUEST_PARAM_ERROR,
                "视频带生词时，不能包含课件信息");
        }

        // 校验生词是否存在
        validateVocabularyExists(reqVO.getVocabularyList());
    }

    /**
     * 校验生词是否存在
     */
    private void validateVocabularyExists(List<VocabularyInfoVO> vocabularyList) {
        if (CollUtil.isNotEmpty(vocabularyList)) {
            List<Long> wordIds = vocabularyList.stream()
                .map(VocabularyInfoVO::getWordId)
                .toList();

            List<WordDO> words = wordService.lambdaQuery()
                .in(WordDO::getId, wordIds)
                .select(WordDO::getId)
                .list();

            if (words.size() != wordIds.size()) {
                throw exception(ErrorCodeConstants.INVALID_REQUEST_PARAM_ERROR, "存在无效的生词ID");
            }
        }
    }

    /**
     * 校验专项练习参数
     */
    private void validateSpecialPracticeParams(InteractiveCourseUnitSaveReqVO reqVO) {
        if (reqVO.getSpecialPracticeInfo() == null) {
            throw exception(ErrorCodeConstants.INVALID_REQUEST_PARAM_ERROR,
                "专项练习类型的单元必须提供练习信息");
        }
    }

    /**
     * 校验真题练习参数
     */
    private void validateRealExamParams(InteractiveCourseUnitSaveReqVO reqVO) {
        if (reqVO.getRealExamInfo() == null) {
            throw exception(ErrorCodeConstants.INVALID_REQUEST_PARAM_ERROR,
                "真题练习类型的单元必须提供练习信息");
        }
    }

    /**
     * 保存课件资源
     */
    private void saveCoursewareResources(Long unitId, List<CoursewareInfoVO> coursewareList) {
        if (unitId == null) {
            return;
        }

        // 先删除该单元下所有课件资源
        coursewareService.lambdaUpdate()
            .eq(InteractiveCourseCoursewareDO::getUnitId, unitId)
            .remove();

        // 先删除该单元下所有课件关联
        unitResourceRelService.lambdaUpdate()
            .eq(InteractiveCourseUnitResourceRelDO::getUnitId, unitId)
            .eq(InteractiveCourseUnitResourceRelDO::getResourceType,
                UnitResourceTypeEnum.COURSEWARE.getCode())
            .remove();

        // 如果没有新的课件资源，直接返回
        if (CollUtil.isEmpty(coursewareList)) {
            return;
        }

        // 添加新的课件资源并收集资源关联
        List<InteractiveCourseUnitResourceRelDO> unitResourceRelDOList = new ArrayList<>();

        for (CoursewareInfoVO courseware : coursewareList) {
            // 创建课件记录
            InteractiveCourseCoursewareDO coursewareDO = new InteractiveCourseCoursewareDO();
            coursewareDO.setMaterialUrl(courseware.getMaterialUrl());
            coursewareDO.setMaterialName(courseware.getMaterialName());
            coursewareDO.setMaterialSize(courseware.getMaterialSize());
            coursewareDO.setUnitId(unitId);
            coursewareDO.setSort(courseware.getSort());

            // 保存单个课件记录以获取ID
            coursewareService.save(coursewareDO);

            // 创建资源关联记录
            InteractiveCourseUnitResourceRelDO relDO = new InteractiveCourseUnitResourceRelDO();
            relDO.setUnitId(unitId);
            relDO.setResourceType(UnitResourceTypeEnum.COURSEWARE.getCode());
            relDO.setResourceId(coursewareDO.getId());
            relDO.setSort(courseware.getSort());
            unitResourceRelDOList.add(relDO);
        }

        // 批量保存资源关联
        if (!unitResourceRelDOList.isEmpty()) {
            unitResourceRelService.saveBatch(unitResourceRelDOList);
        }
    }

    /**
     * 保存生词资源
     */
    private void saveVocabularyResources(Long unitId, List<VocabularyInfoVO> vocabularyList) {
        if (unitId == null) {
            return;
        }

        // 先删除该单元下所有生词关联
        unitResourceRelService.lambdaUpdate()
            .eq(InteractiveCourseUnitResourceRelDO::getUnitId, unitId)
            .eq(InteractiveCourseUnitResourceRelDO::getResourceType,
                UnitResourceTypeEnum.VOCABULARY.getCode())
            .remove();

        // 如果没有新的生词列表，直接返回
        if (CollUtil.isEmpty(vocabularyList)) {
            return;
        }

        // 添加新的生词关联
        List<InteractiveCourseUnitResourceRelDO> unitResourceRelDOList = vocabularyList.stream()
            .map(vocabulary -> {
                InteractiveCourseUnitResourceRelDO relDO = new InteractiveCourseUnitResourceRelDO();
                relDO.setUnitId(unitId);
                relDO.setResourceType(UnitResourceTypeEnum.VOCABULARY.getCode());
                relDO.setResourceId(vocabulary.getWordId());
                relDO.setSort(vocabulary.getSort());
                return relDO;
            })
            .toList();

        unitResourceRelService.saveBatch(unitResourceRelDOList);
    }

    /**
     * 保存专项练习资源
     */
    private void saveSpecialPracticeResource(Long unitId,
        List<SpecialPracticeInfoVO> practiceInfoList, InteractiveCourseUnitDO unitDO) {
        if (unitId == null) {
            return;
        }

        // 删除旧数据
        unitResourceRelService.lambdaUpdate()
            .eq(InteractiveCourseUnitResourceRelDO::getUnitId, unitId)
            .eq(InteractiveCourseUnitResourceRelDO::getResourceType,
                UnitResourceTypeEnum.SPECIAL_PRACTICE.getCode())
            .remove();

        // 如果没有新练习信息，直接返回
        if (CollUtil.isEmpty(practiceInfoList)) {
            return;
        }

        // 添加新数据
        List<InteractiveCourseUnitResourceRelDO> unitResourceRelDOList = practiceInfoList.stream()
            .map(practice -> {
                InteractiveCourseUnitResourceRelDO relDO = new InteractiveCourseUnitResourceRelDO();
                relDO.setUnitId(unitId);
                relDO.setResourceType(UnitResourceTypeEnum.SPECIAL_PRACTICE.getCode());
                relDO.setResourceId(practice.getPracticeGroupId());
                relDO.setSort(practice.getSort());
                return relDO;
            })
            .toList();

        // 保存关联的数据
        unitResourceRelService.saveBatch(unitResourceRelDOList);


        // ===================================设置封面 icon url ===================================
        Long gameId = unitResourceRelDOList.get(0).getResourceId();

        SpecialExercisePageRespDTO pageRespDTO = specialExerciseApi.getQuestionInfo(gameId);
        Integer type = pageRespDTO.getType();

        Map<Integer, String> typeToIconUrl = Map.of(
            SpecialExerciseTypeEnum.KARAOKE.getCode(), InteractiveCourseUnitIconUrlEnum.KARAOKE.getCode(),
            SpecialExerciseTypeEnum.WORD_MATCHING.getCode(), InteractiveCourseUnitIconUrlEnum.WORD_MATCHING.getCode(),
            SpecialExerciseTypeEnum.STROKE_WRITING.getCode(), InteractiveCourseUnitIconUrlEnum.STROKE_WRITING.getCode(),
            SpecialExerciseTypeEnum.WORD_TO_SENTENCE.getCode(), InteractiveCourseUnitIconUrlEnum.SENTENCE_FORMATION.getCode()
        );
        unitDO.setCoverUrl(getConfigByKey(typeToIconUrl.getOrDefault(type, null)));
        // ===================================设置封面 icon url ===================================
    }

    /**
     * 保存真题练习资源
     */
    private void saveRealExamResource(Long unitId, List<InteractiveCourseUnitSaveReqVO.QuestionInfoVO> examInfoList) {
        if (unitId == null) {
            return;
        }

        // 删除旧数据
        unitResourceRelService.lambdaUpdate()
            .eq(InteractiveCourseUnitResourceRelDO::getUnitId, unitId)
            .eq(InteractiveCourseUnitResourceRelDO::getResourceType,
                UnitResourceTypeEnum.QUESTION.getCode())
            .remove();

        // 如果没有新练习信息，直接返回
        if (CollUtil.isEmpty(examInfoList)) {
            return;
        }

        // 添加新数据
        List<InteractiveCourseUnitResourceRelDO> unitResourceRelDOList = examInfoList.stream()
            .map(exam -> {
                InteractiveCourseUnitResourceRelDO relDO = new InteractiveCourseUnitResourceRelDO();
                relDO.setUnitId(unitId);
                relDO.setResourceType(UnitResourceTypeEnum.QUESTION.getCode());
                relDO.setResourceId(exam.getQuestionId());
                relDO.setSort(exam.getSort());
                return relDO;
            })
            .toList();

        unitResourceRelService.saveBatch(unitResourceRelDOList);
    }

    /**
     * 更新互动课单元
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateInteractiveCourseUnitNew(InteractiveCourseUnitSaveReqVO updateReqVO) {
        // 校验参数
        Long updateUnitId = updateReqVO.getId();
        if (updateUnitId == null) {
            throw exception(ErrorCodeConstants.INVALID_REQUEST_PARAM_ERROR, "请选择要更新的单元");
        }

        // 校验课程存在
        validateCourseExists(updateReqVO.getCourseId());

        // 校验单元存在
        InteractiveCourseUnitDO existingUnit = validateInteractiveCourseUnitExists(updateUnitId);

        // 校验参数
        validateUnitParams(updateReqVO);

        // 更新单元基本信息
        InteractiveCourseUnitDO unitDO = InteractiveCourseUnitConvert.INSTANCE.doToDataObject(
            updateReqVO);

        // 检查是否需要更新资源版本号
        if (shouldUpdateResourceVersion(existingUnit, updateReqVO)) {
            Integer currentVersion = existingUnit.getResourceVersion();
            unitDO.setResourceVersion(currentVersion == null ? 1 : currentVersion + 1);
        } else {
            // 保持原版本号
            unitDO.setResourceVersion(existingUnit.getResourceVersion());
        }

        // 清理旧的资源关联
        clearOldResources(updateUnitId);

        // 更新单元视频ID
        if (UnitQuestionSourceTypeEnum.VIDEO.getCode().equals(updateReqVO.getQuestionSource())) {
            videoApi.assetIdIsExist(updateReqVO.getVideoId(),
                VideoSourceEnum.INTERACTIVE_COURSE.getCode());
            unitDO.setVideoId(updateReqVO.getVideoId());
        }

        // 处理关联资源
        processUnitResources(updateUnitId, updateReqVO, unitDO);

        // 更新单元
        interactiveCourseUnitService.updateById(unitDO);

        // 获取课程信息
        InteractiveCourseDO course = interactiveCourseService.getById(unitDO.getCourseId());

        // 设置日志上下文变量
        LogRecordContext.putVariable("unit", unitDO);
        LogRecordContext.putVariable("courseName", course != null ? course.getCourseNameCn() : "未知课程");
    }

    /**
     * 清理旧的资源关联
     * 注：在更新时清理与新单元类型不匹配的旧资源关联
     */
    private void clearOldResources(Long unitId) {
        if (unitId == null) {
            return;
        }

        // 获取单元当前信息
        InteractiveCourseUnitDO unitDO = interactiveCourseUnitService.getById(unitId);
        if (unitDO == null) {
            return;
        }

        // 2. 清理课件资源
        coursewareService.lambdaUpdate()
            .eq(InteractiveCourseCoursewareDO::getUnitId, unitId)
            .remove();

        // 3. 清理所有资源关联（生词、专项练习、真题练习等）
        unitResourceRelService.lambdaUpdate()
            .eq(InteractiveCourseUnitResourceRelDO::getUnitId, unitId)
            .remove();
    }

    /**
     * 获取互动课单元详情（包含关联资源信息）
     *
     * @param id 单元ID
     * @return 互动课单元详情
     */
    public InteractiveCourseUnitRespVO getInteractiveCourseUnitDetail(Long id) {
        // 获取单元基本信息
        InteractiveCourseUnitDO unitDO = validateInteractiveCourseUnitExists(id);

        // 转换为响应VO
        InteractiveCourseUnitRespVO respVO = BeanUtils.toBean(unitDO, InteractiveCourseUnitRespVO.class);

        // 根据不同单元类型加载关联资源
        Integer questionSource = respVO.getQuestionSource();
        if (UnitQuestionSourceTypeEnum.VIDEO.getCode().equals(questionSource)) {
            loadVideoUnitDetails(respVO);
        } else if (UnitQuestionSourceTypeEnum.QUESTION.getCode().equals(questionSource)) {
            loadQuestionUnitDetails(respVO);
        } else if (UnitQuestionSourceTypeEnum.SPECIAL_PRACTICE.getCode().equals(questionSource)) {
            loadSpecialPracticeUnitDetails(respVO);
        }

        return respVO;
    }

    /**
     * 加载视频类型单元的详细信息
     *
     * @param respVO 单元响应VO
     */
    private void loadVideoUnitDetails(InteractiveCourseUnitRespVO respVO) {
        Long unitId = respVO.getId();

        // 通过视频ID查询视频信息
        List<VideoRespDTO> videoRespDTOList = videoApi.getByAssetIdAndSource(respVO.getVideoId(),
            VideoSourceEnum.INTERACTIVE_COURSE.getCode());

        if (CollUtil.isEmpty(videoRespDTOList)) {
            return;
        }
        
        // 设置视频基本信息
        InteractiveCourseUnitRespVO.VideoInfoVO videoInfoVO = new InteractiveCourseUnitRespVO.VideoInfoVO();
        videoInfoVO.setVideoLinkList(parseVideoLinks(videoRespDTOList));
        videoInfoVO.setVideoNameCn(respVO.getVideoNameCn());
        videoInfoVO.setVideoNameEn(respVO.getVideoNameEn());
        videoInfoVO.setVideoNameOt(respVO.getVideoNameOt());
        videoInfoVO.setDuration(videoRespDTOList.get(0).getDuration());
        videoInfoVO.setAspectRatio(respVO.getAspectRatio());
        videoInfoVO.setVideoType(respVO.getVideoType());
        respVO.setVideoInfo(videoInfoVO);


        // 根据视频类型加载额外资源
        Integer videoType = respVO.getVideoType();
        if (VideoTypeEnum.VOCABULARY.getCode().equals(videoType)) {
            loadVocabularyResources(unitId, respVO);
        } else if (VideoTypeEnum.COURSEWARE.getCode().equals(videoType)) {
            loadCoursewareResources(unitId, respVO);
        }
    }

    /**
     * 解析视频链接JSON
     */
    private List<CourseVideoLinkVO> parseVideoLinks(List<VideoRespDTO> videoRespDTOList) {
        if (CollUtil.isEmpty(videoRespDTOList)) {
            return Collections.emptyList();
        }
        return videoRespDTOList.stream()
            .map(video -> {
                CourseVideoLinkVO linkVO = new CourseVideoLinkVO();
                linkVO.setUrl(video.getVideoUrl());
                linkVO.setResolution(VideoQualityEnum.getResolutionByCode(video.getQuality()));
                return linkVO;
            })
            .toList();
    }
    
    /**
     * 加载生词资源
     */
    private void loadVocabularyResources(Long unitId, InteractiveCourseUnitRespVO respVO) {
        // 1. 查询资源关联关系
        List<InteractiveCourseUnitResourceRelDO> resourceRelDOList = unitResourceRelService.lambdaQuery()
            .eq(InteractiveCourseUnitResourceRelDO::getUnitId, unitId)
            .eq(InteractiveCourseUnitResourceRelDO::getResourceType, UnitResourceTypeEnum.VOCABULARY.getCode())
            .orderByAsc(InteractiveCourseUnitResourceRelDO::getSort)
            .list();
        
        if (CollUtil.isEmpty(resourceRelDOList)) {
            return;
        }
        
        // 2. 创建ID到排序序号的映射
        Map<Long, Integer> sortMap = resourceRelDOList.stream()
            .collect(Collectors.toMap(
                InteractiveCourseUnitResourceRelDO::getResourceId,
                InteractiveCourseUnitResourceRelDO::getSort
            ));
        
        // 3. 查询生词信息
        List<Long> wordIds = resourceRelDOList.stream()
            .map(InteractiveCourseUnitResourceRelDO::getResourceId)
            .toList();
            
        List<WordDO> wordDOList = wordService.lambdaQuery()
            .in(WordDO::getId, wordIds)
            .list();

        Map<Long, List<WordMeaningDO>> wordMeaningMap = wordMeaningService.lambdaQuery()
            .in(WordMeaningDO::getWordId, wordIds)
            .list()
            .stream()
            .collect(Collectors.groupingBy(WordMeaningDO::getWordId));

        // 4. 转换为前端展示对象
        respVO.setVocabularyList(wordDOList.stream()
            .map(word -> {
                UnitVocabularyInfoVO vocabularyInfoVO = new UnitVocabularyInfoVO();
                vocabularyInfoVO.setSort(sortMap.get(word.getId()));
                vocabularyInfoVO.setWordId(word.getId());
                vocabularyInfoVO.setWord(word.getWord());
                vocabularyInfoVO.setPinyin(word.getPinyin());
                vocabularyInfoVO.setIsSpecial(word.getIsSpecial());
                vocabularyInfoVO.setHskLevel(word.getHskLevel());
                // 获取等级
                List<Integer> hskLevels = BinaryUtils.getBinaryBitValues(word.getHskLevel());
                StringBuilder hskLevelsDesc = new StringBuilder();
                for (Integer hskLevel : hskLevels) {
                    hskLevelsDesc.append(HskEnum.getDescByCode(hskLevel)).append(SPLIT_CHAR);
                }
                if (!hskLevelsDesc.isEmpty()) {
                    vocabularyInfoVO.setHskLevelDesc(hskLevelsDesc.deleteCharAt(hskLevelsDesc.length() - 1).toString());
                }

                List<WordMeaningDO> wordMeaningDOList = wordMeaningMap.getOrDefault(word.getId(),
                    Collections.emptyList());
                List<Integer> kindSets = wordMeaningDOList.stream()
                    .map(WordMeaningDO::getKind)
                    .filter(Objects::nonNull)
                    .filter(kind -> kind > 0)
                    .toList();
                Set<Integer> kinds = new HashSet<>();
                for (Integer kind : kindSets) {
                    kinds.addAll(BinaryUtils.getBinaryBitValues(kind));
                }
                String kindDesc = joinDescriptions(kinds, WordKindEnum::getDescByValue);
                if (!kinds.isEmpty() && kindSets.size() != wordMeaningDOList.size()) {
                    kindDesc = kindDesc.concat(",-");
                }
                vocabularyInfoVO.setKindsDesc(kindDesc);
                return vocabularyInfoVO;
            })
            .sorted(Comparator.comparing(UnitVocabularyInfoVO::getSort, Comparator.nullsLast(Integer::compareTo)))
            .toList());

        // 只要ID信息 返回给前端方便取参数
        respVO.setWordIdList(resourceRelDOList.stream().map(item -> {
            InteractiveCourseUnitRespVO.VocabularyInfoVO questionInfoVO = new InteractiveCourseUnitRespVO.VocabularyInfoVO();
            questionInfoVO.setWordId(item.getResourceId());
            questionInfoVO.setSort(item.getSort());
            return questionInfoVO;
        }).sorted(Comparator.comparing(InteractiveCourseUnitRespVO.VocabularyInfoVO::getSort, Comparator.nullsLast(Integer::compareTo))).toList());
    }
    private <T> String joinDescriptions(Collection<T> values, Function<T, String> descriptionMapper) {
        if (values == null || values.isEmpty()) {
            return "";
        }
        return values.stream()
            .map(descriptionMapper)
            .filter(Objects::nonNull)
            .collect(Collectors.joining("，"));
    }

    /**
     * 加载课件资源
     */
    private void loadCoursewareResources(Long unitId, InteractiveCourseUnitRespVO respVO) {
        // 1. 查询资源关联关系
        List<InteractiveCourseUnitResourceRelDO> resourceRelDOList = unitResourceRelService.lambdaQuery()
            .eq(InteractiveCourseUnitResourceRelDO::getUnitId, unitId)
            .eq(InteractiveCourseUnitResourceRelDO::getResourceType, UnitResourceTypeEnum.COURSEWARE.getCode())
            .orderByAsc(InteractiveCourseUnitResourceRelDO::getSort)
            .list();

        if (CollUtil.isEmpty(resourceRelDOList)) {
            return;
        }

        // 2. 获取课件ID列表
        List<Long> coursewareIds = resourceRelDOList.stream()
            .map(InteractiveCourseUnitResourceRelDO::getResourceId)
            .toList();

        // 3. 创建ID到排序序号的映射
        Map<Long, Integer> sortMap = resourceRelDOList.stream()
            .collect(Collectors.toMap(
                InteractiveCourseUnitResourceRelDO::getResourceId,
                InteractiveCourseUnitResourceRelDO::getSort
            ));

        // 4. 查询课件详细信息
        List<InteractiveCourseCoursewareDO> coursewareDOList = coursewareService.listByIds(coursewareIds);

        if (CollUtil.isEmpty(coursewareDOList)) {
            return;
        }

        // 5. 转换为VO并按排序序号排序
        respVO.setCoursewareList(coursewareDOList.stream()
            .map(courseware -> {
                CoursewareInfoVO coursewareInfoVO = new CoursewareInfoVO();
                coursewareInfoVO.setId(courseware.getId());
                coursewareInfoVO.setSort(sortMap.get(courseware.getId())); // 使用关联表中的排序
                coursewareInfoVO.setMaterialUrl(courseware.getMaterialUrl());
                coursewareInfoVO.setMaterialSize(courseware.getMaterialSize());
                coursewareInfoVO.setMaterialName(courseware.getMaterialName());
                return coursewareInfoVO;
            })
            .sorted(Comparator.comparing(CoursewareInfoVO::getSort, Comparator.nullsLast(Integer::compareTo)))
            .toList());
    }
    
    /**
     * 加载真题练习类型单元的详细信息
     */
    private void loadQuestionUnitDetails(InteractiveCourseUnitRespVO respVO) {
        Long unitId = respVO.getId();
        
        // 1. 查询资源关联关系
        List<InteractiveCourseUnitResourceRelDO> resourceRelDOList = unitResourceRelService.lambdaQuery()
            .eq(InteractiveCourseUnitResourceRelDO::getUnitId, unitId)
            .eq(InteractiveCourseUnitResourceRelDO::getResourceType, UnitResourceTypeEnum.QUESTION.getCode())
            .orderByAsc(InteractiveCourseUnitResourceRelDO::getSort)
            .list();
            
        if (CollUtil.isEmpty(resourceRelDOList)) {
            return;
        }
        
        // 2. 获取关联的题目ID
        List<Long> questionIds = resourceRelDOList.stream()
            .map(InteractiveCourseUnitResourceRelDO::getResourceId)
            .toList();
            
        // 3. 查询题目详情
        List<QuestionDO> questionDOList = questionService.lambdaQuery()
            .in(QuestionDO::getId, questionIds)
            .list();
            
        if (CollUtil.isEmpty(questionDOList)) {
            return;
        }
        
        // 过滤出启用状态的题目
        questionDOList = questionDOList.stream()
            .filter(question -> CommonStatusEnum.ENABLE.getStatus().equals(question.getStatus()))
            .toList();

        // 检查过滤后是否还有有效题目
        if (CollUtil.isEmpty(questionDOList)) {
            return;
        }

        Map<Long, Integer> sortMap = resourceRelDOList.stream().collect(
            Collectors.toMap(InteractiveCourseUnitResourceRelDO::getResourceId,
                InteractiveCourseUnitResourceRelDO::getSort));

        // 4. 转换为前端展示对象
        List<UnitQuestionInfoVO> questionInfoList = questionDOList.stream()
            .map(question -> {
                UnitQuestionInfoVO questionInfoVO = new UnitQuestionInfoVO();
                questionInfoVO.setQuestionId(question.getId());
                questionInfoVO.setHskLevel(question.getHskLevel());
                questionInfoVO.setIsShow(question.getIsShow());
                questionInfoVO.setQuestionCode(question.getQuestionCode());
                questionInfoVO.setSubject(question.getSubject());
                questionInfoVO.setTextbookId(question.getTextbookId());
                questionInfoVO.setTypeId(question.getTypeId());
                questionInfoVO.setSort(sortMap.get(question.getId()));
                return questionInfoVO;
            })
            .sorted(Comparator.comparing(UnitQuestionInfoVO::getSort, Comparator.nullsLast(Integer::compareTo)))
            .toList();
            
        respVO.setQuestionList(questionInfoList);

        respVO.setQuestionIdList(questionInfoList.stream().map(item -> {
            QuestionInfoVO questionInfoVO = new QuestionInfoVO();
            questionInfoVO.setQuestionId(item.getQuestionId());
            questionInfoVO.setSort(item.getSort());
            return questionInfoVO;
        }).sorted(Comparator.comparing(QuestionInfoVO::getSort, Comparator.nullsLast(Integer::compareTo))).toList());

        // 5. 加载教材类型信息
        loadTextbookInfo(questionDOList, questionInfoList);
    }
    
    /**
     * 加载教材类型信息
     */
    private void loadTextbookInfo(List<QuestionDO> questionDOList, List<UnitQuestionInfoVO> questionInfoList) {
        // 1. 获取所有相关的教材ID
        List<Long> textbookIds = questionDOList.stream()
            .map(QuestionDO::getTextbookId)
            .filter(Objects::nonNull)
            .distinct()
            .toList();
            
        if (CollUtil.isEmpty(textbookIds)) {
            return;
        }

        List<TextbookDO> textbookDOList = textbookService.lambdaQuery()
            .in(TextbookDO::getId, textbookIds)
            .list();
        if (CollUtil.isEmpty(textbookDOList)) {
            return;
        }

        // 2. 查询教材类型
        Map<Long, Integer> textbookTypeMap = textbookDOList
            .stream()
            .collect(Collectors.toMap(TextbookDO::getId, TextbookDO::getType));
            
        // 3. 设置题目对应的教材类型
        questionInfoList.forEach(questionInfo -> {
            Long textbookId = questionInfo.getTextbookId();
            if (textbookId != null && textbookTypeMap.containsKey(textbookId)) {
                questionInfo.setTextbookType(textbookTypeMap.get(textbookId));
            }
        });

        Map<Long, String> textbookNameMap = textbookDOList
            .stream()
            .collect(Collectors.toMap(TextbookDO::getId, TextbookDO::getNameCn));
        questionInfoList.forEach(questionInfo -> {
            Long textbookId = questionInfo.getTextbookId();
            if (textbookId != null && textbookNameMap.containsKey(textbookId)) {
                questionInfo.setTextbookNameCn(textbookNameMap.get(textbookId));
            }
        });
    }
    
    /**
     * 加载专项练习类型单元的详细信息
     */
    private void loadSpecialPracticeUnitDetails(InteractiveCourseUnitRespVO respVO) {
        Long unitId = respVO.getId();
        
        // 1. 查询资源关联关系
        List<InteractiveCourseUnitResourceRelDO> resourceRelDOList = unitResourceRelService.lambdaQuery()
            .eq(InteractiveCourseUnitResourceRelDO::getUnitId, unitId)
            .eq(InteractiveCourseUnitResourceRelDO::getResourceType, UnitResourceTypeEnum.SPECIAL_PRACTICE.getCode())
            .orderByAsc(InteractiveCourseUnitResourceRelDO::getSort)
            .list();
            
        if (CollUtil.isEmpty(resourceRelDOList)) {
            return;
        }
        
        // 2. 获取专项练习ID
        List<Long> specialPracticeIds = resourceRelDOList.stream()
            .map(InteractiveCourseUnitResourceRelDO::getResourceId)
            .toList();
            
        // 3. 调用API获取专项练习信息
        List<SpecialExercisePageRespDTO> specialExercises = specialExerciseApi.getQuestionInfoList(specialPracticeIds);
        
        // 4. 设置第一个练习信息到响应对象
        if (CollUtil.isNotEmpty(specialExercises)) {
            respVO.setSpecialPracticeInfo(
                BeanUtils.toBean(specialExercises.get(0), UnitSpecialPracticeInfoVO.class));
        }
        // 只要ID信息 返回给前端方便取参数
        respVO.setSpecialPracticeIdList(resourceRelDOList.stream().map(item -> {
            InteractiveCourseUnitRespVO.SpecialPracticeInfoVO questionInfoVO = new InteractiveCourseUnitRespVO.SpecialPracticeInfoVO();
            questionInfoVO.setPracticeGroupId(item.getResourceId());
            questionInfoVO.setSort(item.getSort());
            return questionInfoVO;
        }).sorted(Comparator.comparing(InteractiveCourseUnitRespVO.SpecialPracticeInfoVO::getSort, Comparator.nullsLast(Integer::compareTo))).toList());
    }

    /**
     * 判断是否需要更新资源版本号
     * 只有当资源内容发生变化时才更新版本号
     *
     * @param existingUnit 现有单元信息
     * @param updateReqVO  更新请求参数
     * @return 是否需要更新版本号
     */
    private boolean shouldUpdateResourceVersion(InteractiveCourseUnitDO existingUnit, InteractiveCourseUnitSaveReqVO updateReqVO) {
        // 1. 如果单元类型（题目来源）发生变化，需要更新版本
        if (!Objects.equals(existingUnit.getQuestionSource(), updateReqVO.getQuestionSource())) {
            return true;
        }

        // 2. 根据不同的单元类型检查资源是否变化
        Integer questionSource = updateReqVO.getQuestionSource();

        if (UnitQuestionSourceTypeEnum.VIDEO.getCode().equals(questionSource)) {
            // 视频类型：检查视频链接是否全部更换
            return hasVideoLinksChanged(existingUnit.getVideoId(),updateReqVO.getVideoId());
        } else if (UnitQuestionSourceTypeEnum.SPECIAL_PRACTICE.getCode().equals(questionSource)) {
            // 专项练习类型：检查关联的练习组ID是否完全一致
            return hasResourceRelationChanged(existingUnit.getId(), UnitResourceTypeEnum.SPECIAL_PRACTICE.getCode(),
                updateReqVO.getSpecialPracticeInfo(), InteractiveCourseUnitSaveReqVO.SpecialPracticeInfoVO::getPracticeGroupId);
        } else if (UnitQuestionSourceTypeEnum.QUESTION.getCode().equals(questionSource)) {
            // 真题练习类型：检查关联的题目ID是否完全一致
            return hasResourceRelationChanged(existingUnit.getId(), UnitResourceTypeEnum.QUESTION.getCode(),
                updateReqVO.getRealExamInfo(), InteractiveCourseUnitSaveReqVO.QuestionInfoVO::getQuestionId);
        }

        return false;
    }

    /**
     * 检查视频链接是否发生变化
     */
    private boolean hasVideoLinksChanged(String existingVideoId, String newVideoId) {
        return Objects.equals(existingVideoId, newVideoId);
    }

    /**
     * 检查资源关联是否发生变化（通用方法）
     *
     * @param unitId 单元ID
     * @param resourceType 资源类型
     * @param newResourceList 新的资源列表
     * @param resourceIdExtractor 资源ID提取器
     * @return 是否发生变化
     */
    private <T> boolean hasResourceRelationChanged(Long unitId, Integer resourceType,
            List<T> newResourceList, Function<T, Long> resourceIdExtractor) {

        // 获取现有的资源关联
        List<InteractiveCourseUnitResourceRelDO> resourceRelDOList = unitResourceRelService.lambdaQuery()
            .eq(InteractiveCourseUnitResourceRelDO::getUnitId, unitId)
            .eq(InteractiveCourseUnitResourceRelDO::getResourceType, resourceType)
            .list();

        Set<Long> existingResourceIds = resourceRelDOList.stream()
            .map(InteractiveCourseUnitResourceRelDO::getResourceId)
            .collect(Collectors.toSet());

        Set<Long> newResourceIds = CollUtil.isEmpty(newResourceList) ?
            Collections.emptySet() :
            newResourceList.stream()
                .map(resourceIdExtractor)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 比较资源ID集合是否相同
        return !Objects.equals(existingResourceIds, newResourceIds);
    }

    public InteractiveCourseUnitRespVO.VideoInfoVO getVideoInfoByAssetId(String assetId) {
        List<VideoRespDTO> videoRespDTOList = videoApi.getByAssetIdAndSource(assetId,
            VideoSourceEnum.INTERACTIVE_COURSE.getCode());
        // 设置视频基本信息
        InteractiveCourseUnitRespVO.VideoInfoVO videoInfoVO = new InteractiveCourseUnitRespVO.VideoInfoVO();
        videoInfoVO.setVideoLinkList(parseVideoLinks(videoRespDTOList));
        videoInfoVO.setDuration(videoRespDTOList.get(0).getDuration());
        return videoInfoVO;
    }
}