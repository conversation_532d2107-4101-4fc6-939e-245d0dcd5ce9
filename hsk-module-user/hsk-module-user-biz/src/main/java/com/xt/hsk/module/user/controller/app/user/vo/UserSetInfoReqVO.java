package com.xt.hsk.module.user.controller.app.user.vo;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * 设置用户信息传参
 *
 * <AUTHOR>
 * @since 2025/05/30
 */
@Data
public class UserSetInfoReqVO implements Serializable {

    /**
     * 头像
     */
    private String avatar;

    /**
     * 是否首次更新用户信息
     */
    private Boolean firstUpdate;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 当前HSK等级（1-6）
     */
    private Integer currentHskLevel;

    /**
     * 考试时间
     */
    private LocalDate examDate;

    /**
     * 职业（0-其他 1-上班族 2-大学生 3-中学生）
     */
    private Integer profession;

    /**
     * 学习汉语目的（0-其他 1-留学 2-旅游 3-职业发展 4-个人兴趣）
     */
    private Integer learningPurpose;

    /**
     * 汉语水平（0-不确定 1-第一次学中文 2-能简单对话 3-能用中文日常交流）
     */
    private Integer chineseLevel;

    /**
     * 目标HSK等级（1-6）
     */
    private Integer targetHskLevel;

    /**
     * 计划考试时间
     */
    private LocalDateTime plannedExamDate;
}
