package com.xt.hsk.module.edu.controller.app.exam.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.xt.hsk.module.edu.enums.exam.ExamCorrectionStatusEnum;
import com.xt.hsk.module.edu.enums.exam.ExamRecordPracticeStatusEnum;
import com.xt.hsk.module.edu.enums.exam.ExamSubjectSectionsEnum;
import com.xt.hsk.module.edu.enums.exam.ExamTypeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户模考记录 resp vo
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Data
public class UserExamRecordAppRespVO implements VO {

    /**
     * 模考ID
     */
    private Long id;

    /**
     * 模考记录id
     */
    private Long recordId;

    /**
     * 模考名称
     */
    private String examName;
    /**
     * 模考id
     */
    private Long examId;
    /**
     * HSK等级
     */
    private Integer hskLevel;
    /**
     * 模考类型：1-全真模考 2-30分钟模考 3-15分钟模考
     *
     * @see ExamTypeEnum
     */
    @Trans(type = TransType.ENUM, key = "code", target = ExamTypeEnum.class, ref = "examTypeDesc")
    private Integer examType;
    /**
     * 模考类型描述
     */
    private String examTypeDesc;
    /**
     * 参与的模考科目 0-完整模考 1-听力 2-阅读 4-书写
     *
     * @see ExamSubjectSectionsEnum
     */
    @Trans(type = TransType.ENUM, key = "code", target = ExamSubjectSectionsEnum.class, ref = "examSectionsDesc")
    private Integer examSections;
    /**
     * 参与的模考科目描述
     */
    private String examSectionsDesc;
    /**
     * 进行中的模考科目 1-听力 2-阅读 4-书写
     */
    private Integer currentSections;

    /**
     * 总分
     */
    private Integer totalScore;
    /**
     * 总得分
     */
    private Integer actualScore;
    /**
     * 结束作答时间
     */
    private LocalDateTime endTime;
    /**
     * 批改状态 1进行中 2待批改 3已批改
     *
     * @see ExamCorrectionStatusEnum
     */
    private Integer correctionStatus;
    /**
     * 练习状态 1进行中 2已完成
     *
     * @see ExamRecordPracticeStatusEnum
     */
    private Integer practiceStatus;
    /**
     * 进度
     */
    private BigDecimal progress;

}