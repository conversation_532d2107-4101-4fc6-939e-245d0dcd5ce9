package com.xt.hsk.module.game.controller.app.specialexercise.vo;

import com.xt.hsk.module.game.enums.record.GameRecordCorrectTypeEnum;
import lombok.Data;

/**
 * 专项练习报告详情 app RESP VO
 *
 * <AUTHOR>
 * @since 2025-07-12
 */
@Data
public class GameReportDetailsAppRespVO {


    /**
     * 题干内容
     */
    private String questionContent;

    /**
     * 翻译
     */
    private String translationOt;

    /**
     * 是否正确 0错误 1正确
     *
     * @see GameRecordCorrectTypeEnum
     */
    private Integer isCorrect;

    /**
     * 是否被收藏
     */
    private Boolean collect;

    /**
     * 参考答案
     */
    private String referenceAnswer;

    /**
     * 用户答案
     */
    private String userAnswer;

    /**
     * 分数
     */
    private Integer score;

    /**
     * 拼音
     */
    private String pinyin;

    /**
     * 练习组题目版本库ID
     */
    private Long exerciseQuestionVersionId;

    /**
     * 答题状态 0-未答 1-已答 2-失败
     */
    private Integer answerStatus;

    /**
     * 题目ID
     */
    private Long questionId;


}