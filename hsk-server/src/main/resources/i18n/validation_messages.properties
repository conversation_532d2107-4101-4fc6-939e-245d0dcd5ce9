# éç¨æ ¡éªæ¶æ¯
javax.validation.constraints.NotBlank.message=ä¸è½ä¸ºç©º
javax.validation.constraints.NotNull.message=ä¸è½ä¸ºnull
javax.validation.constraints.NotEmpty.message=ä¸è½ä¸ºç©º
javax.validation.constraints.Size.message=å¤§å°å¿é¡»å¨{min}å{max}ä¹é´
javax.validation.constraints.Min.message=å¿é¡»å¤§äºæç­äº{value}
javax.validation.constraints.Max.message=å¿é¡»å°äºæç­äº{value}
javax.validation.constraints.Email.message=ä¸æ¯ææççµå­é®ä»¶å°å
javax.validation.constraints.Pattern.message=æ ¼å¼ä¸æ­£ç¡®
# èªå®ä¹æ ¡éªæ¶æ¯

validation.id-card.invalid=èº«ä»½è¯å·ç æ ¼å¼ä¸æ­£ç¡®
validation.password.weak=å¯ç å¼ºåº¦ä¸å¤
validation.username.invalid=ç¨æ·åæ ¼å¼ä¸æ­£ç¡®
# ============================ç»å½æ³¨åéªè¯æ¶æ¯================================
# å½å®¶åºå·éè¯¯
validation.invalid.country.code=å½å®¶åºå·éè¯¯
#ææºå·æ ¼å¼éè¯¯
validation.mobile.invalid=ææºå·æ ¼å¼éè¯¯
validation.mobile.invalid_2=è¯·è¾å¥æ­£ç¡®çææºå·
#éªè¯ç å¿é¡»æ¯6ä½æ°å­
validation.verification.code.invalid=éªè¯ç å¿é¡»æ¯6ä½æ°å­
#å¯ç æ ¼å¼ä¸æ­£ç¡®
validation.password.invalid=å¯ç éå¨8-16ä½ï¼å¿é¡»åæ¬å­æ¯åæ°å­
