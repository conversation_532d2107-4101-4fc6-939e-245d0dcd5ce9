# ThÃ´ng bÃ¡o xÃ¡c thá»±c chung
javax.validation.constraints.NotBlank.message=khÃ´ng thá» Äá» trá»ng
javax.validation.constraints.NotNull.message=khÃ´ng thá» lÃ  null
javax.validation.constraints.NotEmpty.message=khÃ´ng thá» Äá» trá»ng
javax.validation.constraints.Size.message=kÃ­ch thÆ°á»c pháº£i náº±m trong khoáº£ng {min} vÃ  {max}
javax.validation.constraints.Min.message=pháº£i lá»n hÆ¡n hoáº·c báº±ng {value}
javax.validation.constraints.Max.message=pháº£i nhá» hÆ¡n hoáº·c báº±ng {value}
javax.validation.constraints.Email.message=khÃ´ng pháº£i lÃ  Äá»a chá» email há»£p lá»
javax.validation.constraints.Pattern.message=Äá»nh dáº¡ng khÃ´ng chÃ­nh xÃ¡c
# ThÃ´ng bÃ¡o xÃ¡c thá»±c tÃ¹y chá»nh
validation.id-card.invalid=Äá»nh dáº¡ng sá» tháº» ID khÃ´ng chÃ­nh xÃ¡c
validation.password.weak=Äá» máº¡nh máº­t kháº©u khÃ´ng Äá»§
validation.username.invalid=Äá»nh dáº¡ng tÃªn ngÆ°á»i dÃ¹ng khÃ´ng chÃ­nh xÃ¡c
# ============================ç»å½æ³¨åéªè¯æ¶æ¯================================
# å½å®¶åºå·éè¯¯
validation.invalid.country.code=MÃ£ vÃ¹ng quá»c gia khÃ´ng ÄÃºng
#ææºå·æ ¼å¼éè¯¯
validation.mobile.invalid=Äá»nh dáº¡ng sá» Äiá»n thoáº¡i khÃ´ng ÄÃºng
validation.mobile.invalid_2=Vui lÃ²ng nháº­p ÄÃºng sá» Äiá»n thoáº¡i
#éªè¯ç å¿é¡»æ¯6ä½æ°å­
validation.verification.code.invalid=MÃ£ xÃ¡c minh pháº£i cÃ³ 6 chá»¯ sá»
#å¯ç æ ¼å¼ä¸æ­£ç¡®
validation.password.invalid=Máº­t kháº©u pháº£i tá»« 8-16 kÃ½ tá»±, bao gá»m chá»¯ cÃ¡i vÃ  sá»