package com.xt.hsk.framework.common.enums;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.core.ArrayValuable;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import lombok.Getter;

/**
 * hsk 枚举
 *
 * <AUTHOR>
 * @since 2025/05/24
 */
@Getter
public enum HskEnum implements BasicEnum<Integer>, VO, ArrayValuable<Integer> {
    /**
     * HSK全局通用枚举
     */
    HSK_0(0, "HSK0"),
    HSK_1(1, "HSK1"),
    HSK_2(2, "HSK2"),
    HSK_3(4, "HSK3"),
    HSK_4(8, "HSK4"),
    HSK_5(16, "HSK5"),
    HSK_6(32, "HSK6");

    public final Integer code;
    public final String desc;

    HskEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code) {
        for (HskEnum value : values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    public static HskEnum getByCode(Integer code) {
        for (HskEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static List<Integer> getSubjectByCode(Integer code) {
        if (code == null) {
            return Collections.emptyList();
        }
        if (code.equals(HSK_1.getCode())
            || code.equals(HSK_2.getCode())) {
            return Arrays.asList(SubjectEnum.LISTENING.getCode(), SubjectEnum.READING.getCode());
        } else if (code.equals(HSK_3.getCode())
            || code.equals(HSK_4.getCode())
            || code.equals(HSK_5.getCode())
            || code.equals(HSK_6.getCode())) {
            return Arrays.asList(
                SubjectEnum.LISTENING.getCode(),
                SubjectEnum.READING.getCode(),
                SubjectEnum.WRITING.getCode());
        }

        return Collections.emptyList();
    }

    private static final Integer[] ARRAYS = Arrays.stream(values()).map(HskEnum::getCode).toArray(Integer[]::new);
    @Override
    public Integer[] array() {
        return ARRAYS;
    }
}
