package com.xt.hsk.module.edu.service.question;

import static com.xt.hsk.framework.common.constants.RedisKeyPrefix.QUESTION_ANSWER_CORRECT_COUNT;
import static com.xt.hsk.framework.common.constants.RedisKeyPrefix.QUESTION_ANSWER_COUNT;
import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.*;
import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;

import cn.hutool.http.HtmlUtil;
import cn.idev.excel.EasyExcel;
import cn.idev.excel.exception.ExcelAnalysisException;
import cn.idev.excel.exception.ExcelDataConvertException;
import cn.idev.excel.util.DateUtils;
import com.alibaba.fastjson.JSON;
import com.xt.hsk.framework.common.enums.QuestionTypeEnum;
import com.xt.hsk.framework.common.enums.SubjectEnum;
import com.xt.hsk.framework.common.exception.ServiceException;
import com.xt.hsk.framework.common.pojo.ImportResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.framework.redis.utils.RedisUtil;
import com.xt.hsk.module.edu.controller.admin.question.QuestionCommonStatusVO;
import com.xt.hsk.module.edu.controller.admin.question.vo.AnswerContentVO;
import com.xt.hsk.module.edu.controller.admin.question.vo.OptionContentVO;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionDetailRespVO;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionDetailSaveReqVO;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionPageReqVO;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionRefCountDto;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionRespVO;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionSaveReqVO;
import com.xt.hsk.module.edu.dal.dataobject.question.QuestionDO;
import com.xt.hsk.module.edu.dal.dataobject.question.QuestionExcelDO;
import com.xt.hsk.module.edu.dal.dataobject.question.questiondetail.QuestionDetailDO;
import com.xt.hsk.module.edu.dal.dataobject.question.questiondetailversion.QuestionDetailVersionDO;
import com.xt.hsk.module.edu.dal.dataobject.question.questiontype.QuestionTypeDO;
import com.xt.hsk.module.edu.dal.dataobject.question.questionversion.QuestionVersionDO;
import com.xt.hsk.module.edu.dal.dataobject.sourceQuestion.SourceQuestionDO;
import com.xt.hsk.module.edu.listener.importTask.QuestionImportListener;
import com.xt.hsk.module.edu.service.chapter.ChapterService;
import com.xt.hsk.module.edu.service.exam.ExamDetailService;
import com.xt.hsk.module.edu.service.interactivecourse.InteractiveCourseService;
import com.xt.hsk.module.edu.service.question.questiondetail.QuestionDetailService;
import com.xt.hsk.module.edu.service.question.questiondetailversion.QuestionDetailVersionService;
import com.xt.hsk.module.edu.service.question.questiontype.QuestionTypeService;
import com.xt.hsk.module.edu.service.question.questionversion.QuestionVersionService;
import com.xt.hsk.module.edu.service.sourceQuestion.SourceQuestionService;
import com.xt.hsk.module.edu.service.textbook.TextbookService;
import com.xt.hsk.module.edu.service.unit.UnitService;
import com.xt.hsk.module.edu.util.QuestionContentUtil;
import com.xt.hsk.module.infra.listener.CompositeRowLimitListener;
import com.xt.hsk.module.thirdparty.api.CozeApi;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.FileCopyUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

/**
 * 题目 Manager
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class QuestionManager {

    String questionUrlPrefix = "https://data.hanzii.net";
    @Resource
    private QuestionService questionService;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private QuestionDetailService questionDetailService;
    @Resource
    private QuestionVersionService questionVersionService;
    @Resource
    private QuestionDetailVersionService questionDetailVersionService;
    @Resource
    private UnitService unitService;
    @Resource
    private QuestionTypeService questionTypeService;
    @Resource
    private SourceQuestionService sourceQuestionService;
    @Resource
    private CozeApi cozeApi;
    @Resource
    private TextbookService textbookService;
    @Resource
    private ChapterService chapterService;
    @Resource
    private InteractiveCourseService interactiveCourseService;
    @Resource
    private ExamDetailService examDetailService;

    public static List<OptionContentVO> generateOptions(String correctKey) {
        List<OptionContentVO> options = new ArrayList<>();

        // 校验输入合法性
        if (!"A".equals(correctKey) && !"B".equals(correctKey)) {
            throw new ServiceException(QUESTION_SET_ERROR.getCode(), "仅支持传入'A'或'B'作为正确答案标识");
        }

        // 创建选项A（根据correctKey设置是否为答案）
        OptionContentVO optionContentVO = new OptionContentVO();
        optionContentVO.setKey("A");
        optionContentVO.setContent("正确");
        optionContentVO.setIs_answer("A".equals(correctKey) ? 1 : 0);
        options.add(optionContentVO);
        // 创建选项B（根据correctKey设置是否为答案）
        OptionContentVO optionContentVOB = new OptionContentVO();
        optionContentVOB.setKey("B");
        optionContentVOB.setContent("错误");
        optionContentVOB.setIs_answer("B".equals(correctKey) ? 1 : 0);
        options.add(optionContentVOB);
        return options;
    }

    public static String getQuestionCode(long id) {
        // 取绝对值并转为字符串（处理负数情况）
        String numericStr = String.valueOf(Math.abs(id));

        // 判断数值部分的位数
        int length = numericStr.length();

        if (length <= 6) {
            // 不足6位时补前导零
            return "#" + String.format("%06d", Long.parseLong(numericStr));
        } else {
            // 超过6位时直接使用原数值字符串
            return "#" + numericStr;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Long createQuestion(QuestionSaveReqVO createReqVO) {
        // 插入
        List<QuestionDetailSaveReqVO> questionDetails = createReqVO.getQuestionDetails();
        if (questionDetails == null || questionDetails.isEmpty()) {
            throw new ServiceException(QUESTION_NO_DETAIL);
        }
        createReqVO.setQuestionNum(questionDetails.size());
        Long typeId = createReqVO.getTypeId();
        // 判断选项是否为空
        questionDetails.stream().forEach(detail -> {
            if (CollectionUtils.isNotEmpty(detail.getOptionsContent())) {
                List<OptionContentVO> optionContentVOS = JSON.parseArray(JSON.toJSONString(detail.getOptionsContent()), OptionContentVO.class);
                if (CollectionUtils.isEmpty(optionContentVOS)) {
                    throw new ServiceException(QUESTION_NOT_OPTION);
                }
                // content不能为空
                optionContentVOS.stream().forEach(optionContentVO -> {
                    if (StringUtil.isBlank(optionContentVO.getContent())) {
                        throw new ServiceException(QUESTION_NOT_OPTION);
                    }
                });
            }
        });
        if (QuestionTypeEnum.WRITING_ESSAY.getCode() == typeId) {
            QuestionDetailSaveReqVO detailSaveReqVO = questionDetails.get(0);
            String attachmentImage = detailSaveReqVO.getAttachmentImage();
            String attachmentContent = detailSaveReqVO.getAttachmentContent();
            if (StringUtil.isBlank(attachmentImage) && StringUtil.isBlank(attachmentContent)) {
                throw new ServiceException(QUESTION_NO_DETAIL);
            }
        }
        // 排列顺序题，连词成句题，设置答案
        if (QuestionTypeEnum.READING_ARRANGEMENT.getCode() == typeId
                || QuestionTypeEnum.WRITING_CONSTRUCT_SENTENCE.getCode() == typeId) {
            QuestionDetailSaveReqVO detailSaveReqVO = questionDetails.get(0);
            List optionsContent = detailSaveReqVO.getOptionsContent();
            if (CollectionUtils.isEmpty(optionsContent)) {
                throw new ServiceException(QUESTION_NOT_OPTION);
            }
            // 转化成List<OptionContentVO>对象
            List<OptionContentVO> optionContentVOS = JSON.parseArray(JSON.toJSONString(optionsContent), OptionContentVO.class);
            // 获取key，组成正确答案
            StringBuilder sb = new StringBuilder();
            for (OptionContentVO optionContentVO : optionContentVOS) {
                sb.append(optionContentVO.getKey());
            }
            detailSaveReqVO.setAnswer(sb.toString());
        }
        // 判断科目类型，除了书写题，都需要答案
        if (!Objects.equals(createReqVO.getSubject(), SubjectEnum.WRITING.getCode())) {
            questionDetails.forEach(detailDO -> {
                if (StringUtil.isBlank(detailDO.getAnswer())) {
                    throw new ServiceException(QUESTION_DETAIL_NO_ANSWER);
                }
            });
        }
        // 如果typeId为听力-图片判断题 或者阅读图片判断题,文字判断题
        if (CollectionUtils.isNotEmpty(createReqVO.getOptionsContent())) {
            createReqVO.setOptions(JSON.toJSONString(createReqVO.getOptionsContent()));
        }
        QuestionDO question = BeanUtils.toBean(createReqVO, QuestionDO.class);

        question.setQuestionNum(questionDetails.size());
        // 新增的时候版本设置为1
        question.setVersion(1);
        questionService.save(question);
        String questionCode = getQuestionCode(question.getId());
        question.setQuestionCode(questionCode);
        questionService.updateById(question);
//        QuestionVersionDO questionVersionDO = new QuestionVersionDO();
        // 将question的值设置到questionVersionDO中
        QuestionVersionDO questionVersionDO = BeanUtils.toBean(question, QuestionVersionDO.class);
        questionVersionDO.setQuestionId(question.getId());
        questionVersionDO.setId(null);
        questionVersionService.save(questionVersionDO);
        // 需要处理详情options
        // 查询详情
        for (QuestionDetailSaveReqVO detailDO : questionDetails) {
            List optionsContents = detailDO.getOptionsContent();
            if (QuestionTypeEnum.LISTENING_PICTURE_JUDGE.getCode() == typeId
                    || QuestionTypeEnum.READING_PICTURE_JUDGE.getCode() == typeId
                    || QuestionTypeEnum.LISTENING_TEXT_JUDGE.getCode() == typeId
                    || QuestionTypeEnum.READING_TEXT_JUDGE_QUESTION.getCode() == typeId) {
                // 前端只会传正确答案A，B
                String answer = detailDO.getAnswer();
                if (StringUtil.isEmpty(answer)) {
                    throw new ServiceException(QUESTION_DETAIL_NO_ANSWER);
                }
                optionsContents = generateOptions(answer);
            }
            if (QuestionTypeEnum.WRITING_PICTURE_SENTENCE.getCode() == typeId) {
                if (detailDO.getAttachmentImage() == null || detailDO.getAttachmentImage().isEmpty()) {
                    throw exception(QUESTION_NOT_IMAGE);
                }
                String explainTextCn = detailDO.getExplainTextCn();
                if (explainTextCn == null || explainTextCn.isEmpty()) {
                    throw exception(QUESTION_NOT_EXPLAIN);
                }
                // 将中文解释中的html样式去除
                explainTextCn = HtmlUtil.cleanHtmlTag(explainTextCn);
                Pattern pattern = Pattern.compile("图片描述要素[：:]\\s*(?s)(.+?)\\s*结合");
                Matcher matcher = pattern.matcher(explainTextCn);
                if (!matcher.find()) {
                    throw exception(QUESTION_NOT_IMAGE_DESC);
                }
                detailDO.setAttachmentImageDesc(matcher.group(1));
            }
            if (QuestionTypeEnum.WRITING_ESSAY.getCode() == typeId && detailDO.getAttachmentImage() != null && !detailDO.getAttachmentImage().isEmpty()) {
                String explainTextCn = detailDO.getExplainTextCn();
                if (explainTextCn == null || explainTextCn.isEmpty()) {
                    throw exception(QUESTION_NOT_EXPLAIN);
                }
                // 将中文解释中的html样式去除
                explainTextCn = HtmlUtil.cleanHtmlTag(explainTextCn);
                Pattern pattern = Pattern.compile("图片描述要素[：:]\\s*(.+?)\\s*2.文章大纲");
                Matcher matcher = pattern.matcher(explainTextCn);
                if (!matcher.find()) {
                    throw exception(QUESTION_NOT_IMAGE_DESC);
                }
                detailDO.setAttachmentImageDesc(matcher.group(1));
            }
            detailDO.setOptions(JSON.toJSONString(optionsContents));
            detailDO.setQuestionId(question.getId());
            detailDO.setVersion(1);
        }
        List<QuestionDetailDO> detailDOS = BeanUtils.toBean(questionDetails, QuestionDetailDO.class);
        questionDetailService.saveBatch(detailDOS);


        List<QuestionDetailVersionDO> detailVersionList = new ArrayList<>();
        for (QuestionDetailDO detailDO : detailDOS) {
//            QuestionDetailVersionDO questionDetailVersionDO = new QuestionDetailVersionDO();
            QuestionDetailVersionDO questionDetailVersionDO = BeanUtils.toBean(detailDO, QuestionDetailVersionDO.class);
            questionDetailVersionDO.setQuestionDetailId(detailDO.getId());
            questionDetailVersionDO.setQuestionId(question.getId());
            questionDetailVersionDO.setId(null);
            detailVersionList.add(questionDetailVersionDO);
        }
        questionDetailVersionService.saveBatch(detailVersionList);
        // 返回
        return question.getId();
    }

    public void updateQuestion(QuestionSaveReqVO updateReqVO) {
        // 校验存在
        Long typeId = updateReqVO.getTypeId();
        validateQuestionExists(updateReqVO.getId());
        QuestionRespVO question = questionService.getQuestionById(updateReqVO.getId());
        if (question == null) {
            throw exception(QUESTION_NOT_EXISTS);
        }
        List<QuestionDetailSaveReqVO> questionDetails = updateReqVO.getQuestionDetails();
        if (questionDetails == null || questionDetails.isEmpty()) {
            throw new ServiceException(QUESTION_NO_DETAIL);
        }
        // 查询题目是否在模考引用
        long counted = examDetailService.countQuestionQuote(Collections.singletonList(updateReqVO.getId()));
        if (counted > 0) {
            List<QuestionDetailDO> questionDetailDOS = questionDetailService.selectListByQuestionId(updateReqVO.getId());
            // 被模考引用的题目不能增加和减少题号
            List<QuestionDetailSaveReqVO> saveReqVOS = questionDetails.stream().filter(t -> {
                return t.getId() == null;
            }).toList();
            boolean flag = false;
            if (!saveReqVOS.isEmpty()) {
                // 不能新增
                flag = true;
            }
            Set<Long> collect = questionDetails.stream().filter(t -> {
                return t.getId() != null;
            }).map(QuestionDetailSaveReqVO::getId).collect(Collectors.toSet());
            for (QuestionDetailDO questionDetailDO : questionDetailDOS) {
                if (!collect.contains(questionDetailDO.getId())) {
                    // 不能删除
                    flag = true;
                    break;
                }
            }
            if (flag) {
                throw new ServiceException(500, "该题目已被模考引用，请删除对应被引用的内容后删除/新增题号");
            }

        }
        updateReqVO.setQuestionNum(questionDetails.size());
        questionDetails.stream().forEach(detail -> {
            if (CollectionUtils.isNotEmpty(detail.getOptionsContent())) {
                List<OptionContentVO> optionContentVOS = JSON.parseArray(JSON.toJSONString(detail.getOptionsContent()), OptionContentVO.class);
                if (CollectionUtils.isEmpty(optionContentVOS)) {
                    throw new ServiceException(QUESTION_NOT_OPTION);
                }
                // content不能为空
                optionContentVOS.stream().forEach(optionContentVO -> {
                    if (StringUtil.isBlank(optionContentVO.getContent())) {
                        throw new ServiceException(QUESTION_NOT_OPTION);
                    }
                });
            }
        });
        if (QuestionTypeEnum.WRITING_ESSAY.getCode() == typeId) {
            QuestionDetailSaveReqVO detailSaveReqVO = questionDetails.get(0);
            String attachmentImage = detailSaveReqVO.getAttachmentImage();
            String attachmentContent = detailSaveReqVO.getAttachmentContent();
            if (StringUtil.isBlank(attachmentImage) && StringUtil.isBlank(attachmentContent)) {
                throw new ServiceException(QUESTION_NO_DETAIL);
            }
        }
        // 排列顺序题，连词成句题，设置答案
        if (QuestionTypeEnum.READING_ARRANGEMENT.getCode() == typeId
                || QuestionTypeEnum.WRITING_CONSTRUCT_SENTENCE.getCode() == typeId) {
            QuestionDetailSaveReqVO detailSaveReqVO = questionDetails.get(0);
            List optionsContent = detailSaveReqVO.getOptionsContent();
            if (CollectionUtils.isEmpty(optionsContent)) {
                throw new ServiceException(QUESTION_NOT_OPTION);
            }
            // 转化成List<OptionContentVO>对象
            List<OptionContentVO> optionContentVOS = JSON.parseArray(JSON.toJSONString(optionsContent), OptionContentVO.class);
            // 获取key，组成正确答案
            StringBuilder sb = new StringBuilder();
            for (OptionContentVO optionContentVO : optionContentVOS) {
                sb.append(optionContentVO.getKey());
            }
            detailSaveReqVO.setAnswer(sb.toString());
        }

        if (!Objects.equals(updateReqVO.getSubject(), SubjectEnum.WRITING.getCode())) {
            questionDetails.forEach(detailDO -> {
                if (StringUtil.isBlank(detailDO.getAnswer())) {
                    throw new ServiceException(QUESTION_DETAIL_NO_ANSWER);
                }
            });
        }
        // 更新
        // 查出版本号
        if (CollectionUtils.isNotEmpty(updateReqVO.getOptionsContent())) {
            updateReqVO.setOptions(JSON.toJSONString(updateReqVO.getOptionsContent()));
        }
        QuestionDO updateObj = BeanUtils.toBean(updateReqVO, QuestionDO.class);
        // 更新版本号+1
        Integer version = questionService.getById(updateReqVO.getId()).getVersion();
        updateObj.setVersion(version + 1);
        questionService.updateById(updateObj);
//        QuestionVersionDO questionVersionDO = new QuestionVersionDO();
        QuestionVersionDO questionVersionDO = BeanUtils.toBean(updateObj, QuestionVersionDO.class);
        questionVersionDO.setQuestionId(updateObj.getId());
        questionVersionDO.setId(null);
        questionVersionDO.setQuestionNum(updateObj.getQuestionNum());
        questionVersionService.save(questionVersionDO);
        // 更新详情
        for (QuestionDetailSaveReqVO detailDO : questionDetails) {
            List<OptionContentVO> optionContentVOS = detailDO.getOptionsContent();
            if (QuestionTypeEnum.LISTENING_PICTURE_JUDGE.getCode() == typeId
                    || QuestionTypeEnum.READING_PICTURE_JUDGE.getCode() == typeId
                    || QuestionTypeEnum.LISTENING_TEXT_JUDGE.getCode() == typeId
                    || QuestionTypeEnum.READING_TEXT_JUDGE_QUESTION.getCode() == typeId) {
                // 前端只会传正确答案A，B
                String answer = detailDO.getAnswer();
                optionContentVOS = generateOptions(answer);
            }
            detailDO.setOptions(JSON.toJSONString(optionContentVOS));
            detailDO.setVersion(updateObj.getVersion());
            // 判断是否存在id
            if (detailDO.getId() == null) {
                detailDO.setQuestionId(updateReqVO.getId());
            }
            if (QuestionTypeEnum.WRITING_PICTURE_SENTENCE.getCode() == typeId) {
                if (detailDO.getAttachmentImage() == null || detailDO.getAttachmentImage().isEmpty()) {
                    throw exception(QUESTION_NOT_IMAGE);
                }
                String explainTextCn = detailDO.getExplainTextCn();
                if (explainTextCn == null || explainTextCn.isEmpty()) {
                    throw exception(QUESTION_NOT_EXPLAIN);
                }
                // 将中文解释中的html样式去除
                explainTextCn = HtmlUtil.cleanHtmlTag(explainTextCn);
                Pattern pattern = Pattern.compile("图片描述要素[：:]\\s*(?s)(.+?)\\s*结合");
                Matcher matcher = pattern.matcher(explainTextCn);
                if (!matcher.find()) {
                    throw exception(QUESTION_NOT_IMAGE_DESC);
                }
                detailDO.setAttachmentImageDesc(matcher.group(1));
            }
            if (QuestionTypeEnum.WRITING_ESSAY.getCode() == typeId && detailDO.getAttachmentImage() != null && !detailDO.getAttachmentImage().isEmpty()) {
                String explainTextCn = detailDO.getExplainTextCn();
                if (explainTextCn == null || explainTextCn.isEmpty()) {
                    throw exception(QUESTION_NOT_EXPLAIN);
                }
                // 将中文解释中的html样式去除
                explainTextCn = HtmlUtil.cleanHtmlTag(explainTextCn);
                Pattern pattern = Pattern.compile("图片描述要素[：:]\\s*(.+?)\\s*2.文章大纲");
                Matcher matcher = pattern.matcher(explainTextCn);
                if (!matcher.find()) {
                    throw exception(QUESTION_NOT_IMAGE_DESC);
                }
                detailDO.setAttachmentImageDesc(matcher.group(1));
            }
        }
        List<QuestionDetailDO> detailDOS = BeanUtils.toBean(updateReqVO.getQuestionDetails(), QuestionDetailDO.class);
        List<QuestionDetailDO> questionDetailDOSDB = questionDetailService.selectListByQuestionId(updateReqVO.getId());
        // 判断questionDetailDOSDB 和 detailDOS的id匹配度，找出不匹配的，找出存在在旧数据中但是不存在在新数据中的
        Set<Long> newIds = detailDOS.stream()
                .map(QuestionDetailDO::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Set<Long> oldIds = questionDetailDOSDB.stream()
                .map(QuestionDetailDO::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Set<Long> idsToDelete = new HashSet<>(oldIds);
        idsToDelete.removeAll(newIds);
        // 需要处理options
        questionDetailService.saveOrUpdateBatch(detailDOS);
        // 删除对应的不存在detail
        questionDetailService.removeBatchByIds(idsToDelete);
        List<QuestionDetailVersionDO> detailVersionList = new ArrayList<>();
        for (QuestionDetailDO detailDO : detailDOS) {
            // 版本表数据
//            QuestionDetailVersionDO questionDetailVersionDO = new QuestionDetailVersionDO();
            QuestionDetailVersionDO questionDetailVersionDO = BeanUtils.toBean(detailDO, QuestionDetailVersionDO.class);
            questionDetailVersionDO.setQuestionDetailId(detailDO.getId());
            questionDetailVersionDO.setId(null);
            detailVersionList.add(questionDetailVersionDO);
        }
        questionDetailVersionService.saveBatch(detailVersionList);
    }

    /**
     * 判断真题是否被引用
     */
    public void isQuestionUsed(List<Long> questionIds) {
        // 互动课
        int flag = 0;
        long counted = interactiveCourseService.countQuestionQuote(questionIds);
        if (counted > 0) {
            flag = 1;
//            throw new ServiceException(500,"该题目已被互动课引用，请删除对应被引用的内容后禁用/删除");
        }
        // 模考
        long counted1 = examDetailService.countQuestionQuote(questionIds);
        if (counted1 > 0) {
            flag += 2;
        }
        if (flag == 1) {
            throw new ServiceException(500, "该题目已被互动课引用，请删除对应被引用的内容后禁用/删除");
        } else if (flag == 2) {
            throw new ServiceException(500, "该题目已被模考引用，请删除对应被引用的内容后禁用/删除");
        } else if (flag == 3) {
            throw new ServiceException(500, "该题目已被互动课和模考引用，请删除对应被引用的内容后禁用/删除");
        }
//        return false;
    }

    /**
     * 题目的启用，禁用，删除，隐藏等功能
     */
    public void updateQuestionCommonStatus(QuestionCommonStatusVO questionCommonStatusVO) {
        // 校验存在
        Long id = questionCommonStatusVO.getId();
        List<Long> ids = questionCommonStatusVO.getIds();
        if (id == null && CollectionUtils.isEmpty(ids)) {
            throw exception(QUESTION_NOT_EXISTS);
        }
        if (questionCommonStatusVO.getDeleted() != null) {
            if (id != null) {
                // 判断题目是否被引用
                isQuestionUsed(Collections.singletonList(id));
                questionService.removeById(id);
                List<QuestionDetailDO> questionDetails = questionDetailService.selectListByQuestionId(id);
                List<Long> questionDetailIds = questionDetails.stream().map(QuestionDetailDO::getId).toList();

                questionDetailService.removeBatchByIds(questionDetailIds);
//                // 同步删除历史版本库
//                LambdaUpdateWrapper<QuestionVersionDO> questionVersionUpdateWrapper = new LambdaUpdateWrapper<>();
//                questionVersionUpdateWrapper.eq(QuestionVersionDO::getQuestionId, id);
//                questionVersionService.remove(questionVersionUpdateWrapper);
                return;
            }
            if (CollectionUtils.isNotEmpty(ids)) {
                List<QuestionDO> questionDOList = questionService.listByIds(ids);
                isQuestionUsed(ids);
                questionService.removeBatchByIds(ids);
                questionDOList.forEach(questionDO -> {
                    List<QuestionDetailDO> questionDetails = questionDetailService.selectListByQuestionId(questionDO.getId());
                    List<Long> questionDetailIds = questionDetails.stream().map(QuestionDetailDO::getId).toList();
                    questionDetailService.removeBatchByIds(questionDetailIds);
                });
//                // 同步删除历史版本库
//                LambdaUpdateWrapper<QuestionVersionDO> questionVersionUpdateWrapper = new LambdaUpdateWrapper<>();
//                questionVersionUpdateWrapper.in(QuestionVersionDO::getQuestionId, ids);
//                questionVersionService.remove(questionVersionUpdateWrapper);
                return;
            }
        }
        if (questionCommonStatusVO.getId() != null) {
            QuestionDO questionDO = questionService.getById(id);
            if (questionCommonStatusVO.getStatus() != null) {
                if (questionCommonStatusVO.getStatus() == 1) {
                    // 判断题目是否被引用
                    isQuestionUsed(Collections.singletonList(id));
                }
                questionDO.setStatus(questionCommonStatusVO.getStatus());
            }

            if (questionCommonStatusVO.getIsShow() != null) {
                questionDO.setIsShow(questionCommonStatusVO.getIsShow());
            }
            questionService.updateById(questionDO);
            return;
        }
        if (CollectionUtils.isNotEmpty(ids)) {
            List<QuestionDO> questionDOList = questionService.listByIds(ids);
            if (questionCommonStatusVO.getStatus() != null && questionCommonStatusVO.getStatus() == 1) {
                // 判断题目是否被引用
                isQuestionUsed(ids);
            }
            questionDOList.forEach(questionDO -> {
                if (questionCommonStatusVO.getStatus() != null) {
                    questionDO.setStatus(questionCommonStatusVO.getStatus());
                }
                if (questionCommonStatusVO.getIsShow() != null) {
                    questionDO.setIsShow(questionCommonStatusVO.getIsShow());
                }
            });
            questionService.updateBatchById(questionDOList);
        }
    }

    private void validateQuestionExists(Long id) {
        if (questionService.getById(id) == null) {
            throw exception(DATA_NOT_EXIST);
        }
    }

    public QuestionDO getQuestion(Long id) {
        QuestionDO questionDO = questionService.getById(id);
        String options = questionDO.getOptions();
        if (StringUtil.isNotBlank(options)) {
            JSON.parse(options);
        }
        return questionDO;
    }

    public PageResult<QuestionRespVO> getQuestionPage(@Valid QuestionPageReqVO pageReqVO) {
        PageResult<QuestionRespVO> pageResult = questionService.queryQuestionPage(pageReqVO);
        if (pageResult == null || pageResult.getList().isEmpty()) {
            return PageResult.empty();
        }
        Set<Long> questionIds = pageResult.getList().stream().map(QuestionRespVO::getId).collect(Collectors.toSet());
        List<QuestionRefCountDto> interactiveCourseQuestionQuoteCounts = interactiveCourseService.getInteractiveCourseQuestionQuoteCount(questionIds);
        Map<Long, Integer> interactiveCountMap = interactiveCourseQuestionQuoteCounts.stream().collect(Collectors.toMap(QuestionRefCountDto::getQuestionId, QuestionRefCountDto::getReferenceCount, (k1, k2) -> k1));
        List<QuestionRefCountDto> examQuestionQuoteCounts = examDetailService.getExamQuestionQuoteCount(questionIds);
        Map<Long, Integer> examCountMap = examQuestionQuoteCounts.stream().collect(Collectors.toMap(QuestionRefCountDto::getQuestionId, QuestionRefCountDto::getReferenceCount, (k1, k2) -> k1));
        String date = DateUtils.format(new Date(), DateUtils.DATE_FORMAT_10);
        pageResult.getList().forEach(item -> {
            String answerCorrectCountKey = String.format(QUESTION_ANSWER_CORRECT_COUNT, date, item.getId());
            String answerCountKey = String.format(QUESTION_ANSWER_COUNT, date, item.getId());
            Integer answerCount = item.getTotalAnswerCount() == null ? 0 : item.getTotalAnswerCount();
            Integer answerCorrectCount = item.getCorrectAnswerCount() == null ? 0 : item.getCorrectAnswerCount();
            try {
                answerCount += redisUtil.getInteger(answerCountKey) == null ? 0 : redisUtil.getInteger(answerCountKey);
                answerCorrectCount += redisUtil.getInteger(answerCorrectCountKey) == null ? 0 : redisUtil.getInteger(answerCorrectCountKey);
            } catch (Exception e) {
                log.error("统计题目正确率Redis get操作失败: key={}", answerCountKey, e);
            }
            item.setCorrectRate(answerCount == 0 ? 0 : answerCorrectCount / answerCount);
            item.setTotalAnswerCount(answerCount);
            item.setCorrectAnswerCount(answerCorrectCount);
            // 解析option
            String options = item.getOptions();
            if (StringUtil.isNotBlank(options)) {
                item.setOptionsContent(QuestionContentUtil.optionConvert(options));
                item.setOptions(null);
            }
            item.setReferenceCount(interactiveCountMap.getOrDefault(item.getId(), 0) + examCountMap.getOrDefault(item.getId(), 0));
        });
        return pageResult;
    }

    public Long getPageCount(@Valid QuestionPageReqVO pageReqVO) {
        return questionService.queryQuestionCount(pageReqVO);
    }

    public void executeDate() {
        List<Long> longList = questionService.queryQuestionIdList();
        for (Long questionId : longList) {
            QuestionRespVO question = questionService.getQuestionById(questionId);
            // 注意处理option
            //更新材料
            questionService.updateById(QuestionContentUtil.materialConvert(question));
            //更新题目
        }
    }

    public void downloadImportTemplate(HttpServletResponse response) {
        try {
            // 设置响应头
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("批量导入题目模板", StandardCharsets.UTF_8);
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

            // 获取模板文件路径
            ClassPathResource resource = new ClassPathResource("excelTemplate/hsk真题导入模版.xlsx");
            InputStream inputStream = resource.getInputStream();

            // 写入响应流
            FileCopyUtils.copy(inputStream, response.getOutputStream());
        } catch (Exception e) {
            log.error("下载导入题目模板失败", e);
            throw new ServiceException(IMPORT_DOWNLOAD_TEMPLATE_ERROR);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public ImportResult importQuestion(MultipartFile file) {
        // 查询出所有的未删除的题型
        List<QuestionTypeDO> questionTypeDOList = questionTypeService.list();

        // 创建subject和nameCn的联合对应id的映射
        HashMap<String, Long> questionTypeMap = questionTypeDOList.stream()
                .collect(Collectors.toMap(
                        // 联合键生成逻辑：SubjectEnum编码 + "_" + nameCn
                        doObj -> {
                            // 1. 转换subject到枚举（处理可能的空值）
                            SubjectEnum subjectEnum = SubjectEnum.getByCode(doObj.getSubject());
                            if (subjectEnum == null) {
                                throw new IllegalArgumentException("无效的subject代码: " + doObj.getSubject());
                            }

                            // 2. 检查nameCn非空
                            String nameCn = doObj.getNameCn();
                            if (nameCn == null || nameCn.isEmpty()) {
                                throw new IllegalArgumentException("nameCn不能为空");
                            }

                            // 3. 生成联合键（可根据需求调整分隔符）
                            return subjectEnum.getDesc() + "_" + nameCn;
                        },
                        QuestionTypeDO::getId,
                        // 键重复时的处理策略（这里选择用后者覆盖前者，可根据需求调整）
                        (existing, replacement) -> replacement,
                        HashMap::new
                ));
        ImportResult result = new ImportResult();
        try {
//            // 创建数据处理监听器
//            ExamImportListener dataListener = new ExamImportListener(examService, examCenterService, areaService);
            QuestionImportListener dataListener = new QuestionImportListener(unitService, questionTypeMap, this);
//
            // 创建组合监听器，限制最多导入300个考试场次
            CompositeRowLimitListener<QuestionExcelDO> compositeListener =
                    new CompositeRowLimitListener<>(300, dataListener, "一次最多导入300个题目，请分批导入");
//
            // 执行导入，设置headRowNumber=2表示从第三行开始读取数据
            EasyExcel.read(file.getInputStream(), QuestionExcelDO.class, compositeListener)
                    // 设置表头占用2行，数据从第3行开始
                    .headRowNumber(2)
                    .sheet()
                    .doRead();

            result.setSuccess(dataListener.SUCCESS);
            result.setMsg(dataListener.msg);
            result.setInvalidCount(dataListener.INVALID_COUNT);
            result.setValidCount(dataListener.VALID_COUNT);
        } catch (ExcelDataConvertException e) {
            log.error("导入题目失败: {}", e.getMessage(), e);
            result.setSuccess(false);
            result.setMsg(Collections.singletonList("导入失败：" + "请检查一下模版是否正确！"));
            result.setInvalidCount(0);
            result.setValidCount(0);
        } catch (ExcelAnalysisException e) {
            log.warn("导入题目受限: {}", e.getMessage(), e);
            result.setSuccess(false);
            result.setMsg(Collections.singletonList(e.getMessage()));
            result.setInvalidCount(0);
            result.setValidCount(0);
        } catch (Exception e) {
            log.error("导入题目失败: {}", e.getMessage(), e);
            result.setSuccess(false);
            result.setMsg(Collections.singletonList("导入失败：" + e.getMessage()));
            result.setInvalidCount(0);
            result.setValidCount(0);
        }

        return result;
    }

    public QuestionRespVO getQuestionDetail(Long questionId) {
        // 1.先通过questionId查询出对应的材料，要关联教材单元章节
        QuestionRespVO questionRespVO = questionService.getQuestionById(questionId);
        // 处理options
        if (StringUtil.isNotBlank(questionRespVO.getOptions())) {
            questionRespVO.setOptionsContent(QuestionContentUtil.optionConvert(questionRespVO.getOptions()));
            questionRespVO.setOptions(null);
        }
        List<QuestionDetailDO> questionDetail = questionDetailService.selectListByQuestionId(questionId);
        if (questionDetail == null || questionDetail.isEmpty()) {
            return questionRespVO;
        }
        List<QuestionDetailRespVO> detailRespVOS = QuestionContentUtil.questionDetailConvert(questionDetail);
        for (QuestionDetailRespVO detailRespVO : detailRespVOS) {
            if (StringUtil.isNotBlank(detailRespVO.getOptions())) {
                detailRespVO.setOptionsContent(QuestionContentUtil.optionConvert(detailRespVO.getOptions()));
                detailRespVO.setOptions(null);
            }
        }
        questionRespVO.setQuestionDetails(detailRespVOS);
        return questionRespVO;
    }

    public void executeData() {
        // 查询出所有type_id=1的题
        List<Integer> typeList = new ArrayList<>();
        typeList.add(20);
        /*typeList.add(11);
        typeList.add(19);
        typeList.add(18);
        typeList.add(17);
        typeList.add(12);
        typeList.add(13);
        typeList.add(1);
        typeList.add(2);
        typeList.add(4);
        typeList.add(6);
        typeList.add(7);*/

        for (Integer typeId : typeList) {
            QuestionPageReqVO questionPageReqVO = new QuestionPageReqVO();
            questionPageReqVO.setPageSize(1);
            questionPageReqVO.setPageSize(5000);
            questionPageReqVO.setTypeId(typeId);
            PageResult<QuestionDO> questionDOPageResult = questionService.selectPage(questionPageReqVO);
            List<QuestionDO> doPageResultList = questionDOPageResult.getList();
            for (QuestionDO questionDO : doPageResultList) {
                List<QuestionDetailDO> questionDetailDOS = questionDetailService.selectListByQuestionId(questionDO.getId());
                if (questionDetailDOS.size() > 1) {
                    log.info("异常数据，questionId：{}", questionDO.getId());
                }
                QuestionDetailDO questionDetailDO = questionDetailDOS.get(0);
                if (StringUtil.isEmpty(questionDetailDO.getAttachmentAudio())) {
                    questionDetailDO.setAttachmentAudio(questionDO.getMaterialAudio());
                }
                if (StringUtil.isEmpty(questionDetailDO.getAttachmentImage())) {
                    questionDetailDO.setAttachmentImage(questionDO.getMaterialImage());
                }
                if (StringUtil.isEmpty(questionDetailDO.getAttachmentContent())) {
                    questionDetailDO.setAttachmentContent(questionDO.getMaterialContent());
                }
                if (StringUtil.isEmpty(questionDetailDO.getOptions())) {
                    questionDetailDO.setOptions(questionDO.getOptions());
                }
                questionDetailService.updateById(questionDetailDO);
            }
        }
    }


    public void executeData21() {
        // 查询出所有type_id=1的题
        List<Integer> typeList = new ArrayList<>();
        typeList.add(21);
        for (Integer typeId : typeList) {
            QuestionPageReqVO questionPageReqVO = new QuestionPageReqVO();
            questionPageReqVO.setPageSize(1);
            questionPageReqVO.setPageSize(5000);
            questionPageReqVO.setTypeId(typeId);
            PageResult<QuestionDO> questionDOPageResult = questionService.selectPage(questionPageReqVO);
            List<QuestionDO> doPageResultList = questionDOPageResult.getList();
            for (QuestionDO questionDO : doPageResultList) {
                List<QuestionDetailDO> questionDetailDOS = questionDetailService.selectListByQuestionId(questionDO.getId());
                if (questionDetailDOS.size() > 1) {
                    log.info("异常数据，questionId：{}", questionDO.getId());
                }
                QuestionDetailDO questionDetailDO = questionDetailDOS.get(0);
                String options = questionDO.getOptions();
                // 处理options
                if (StringUtil.isNotBlank(options)) {
                    List<OptionContentVO> optionContentVOS = QuestionContentUtil.optionConvert(options);
                    // 将optionContentVOS 的content取出来，然后用空格连接
                    String content = optionContentVOS.stream().map(OptionContentVO::getContent).collect(Collectors.joining(" "));
                    // 去掉最后一个空格
                    questionDetailDO.setAttachmentContent(content);
                }
                questionDetailService.updateById(questionDetailDO);
            }
        }
    }

    public void addUrl() {
        // 查询出所有type_id=1的题
        List<Integer> typeList = new ArrayList<>();
        typeList.add(1);
        typeList.add(2);
        typeList.add(3);
        typeList.add(4);
        typeList.add(5);
        typeList.add(6);
        typeList.add(7);
        typeList.add(8);
        typeList.add(14);
        typeList.add(19);
        typeList.add(22);
        for (Integer typeId : typeList) {
            QuestionPageReqVO questionPageReqVO = new QuestionPageReqVO();
            questionPageReqVO.setPageSize(1);
            questionPageReqVO.setPageSize(5000);
            questionPageReqVO.setTypeId(typeId);
            PageResult<QuestionDO> questionDOPageResult = questionService.selectPage(questionPageReqVO);
            List<QuestionDO> doPageResultList = questionDOPageResult.getList();
            for (QuestionDO questionDO : doPageResultList) {
                String materialAudio = questionDO.getMaterialAudio();
                if (StringUtil.isNotBlank(materialAudio)) {
                    questionDO.setMaterialAudio(questionUrlPrefix + materialAudio);
                }
                String materialImage = questionDO.getMaterialImage();
                if (StringUtil.isNotBlank(materialImage)) {
                    questionDO.setMaterialImage(questionUrlPrefix + materialImage);
                }
                List<QuestionDetailDO> questionDetailDOS = questionDetailService.selectListByQuestionId(questionDO.getId());
                for (QuestionDetailDO questionDetailDO : questionDetailDOS) {
                    String attachmentAudio = questionDetailDO.getAttachmentAudio();
                    String attachmentImage = questionDetailDO.getAttachmentImage();
                    if (StringUtil.isNotBlank(attachmentAudio)) {
                        attachmentAudio = attachmentAudio.replace("https://data.hanzii.net/uploads", "");
                        questionDetailDO.setAttachmentAudio(questionUrlPrefix + attachmentAudio);
                    }
                    if (StringUtil.isNotBlank(attachmentImage)) {
                        attachmentImage = attachmentImage.replace("https://data.hanzii.net/uploads", "");
                        questionDetailDO.setAttachmentImage(questionUrlPrefix + attachmentImage);
                    }
                    if (questionDO.getTypeId() == 2) {
                        String options = questionDetailDO.getOptions();
                        if (StringUtil.isNotBlank(options)) {
                            List<OptionContentVO> optionContentVOS = QuestionContentUtil.optionConvert(options);
                            for (OptionContentVO optionContentVO : optionContentVOS) {
                                String content = optionContentVO.getContent();
                                if (StringUtil.isNotBlank(content)) {
                                    content = content.replace("https://data.hanzii.net/uploads", "");
                                    optionContentVO.setContent(questionUrlPrefix + content);
                                }
                            }
                            questionDetailDO.setOptions(JSON.toJSONString(optionContentVOS));
                        }
                    }
                    questionDetailService.updateById(questionDetailDO);
                }
                if (questionDO.getTypeId() == 1 || questionDO.getTypeId() == 4
                        || questionDO.getTypeId() == 5 || questionDO.getTypeId() == 6
                        || questionDO.getTypeId() == 7 || questionDO.getTypeId() == 14 ||
                        questionDO.getTypeId() == 19 || questionDO.getTypeId() == 22) {
                }
                if (questionDO.getTypeId() == 3 || questionDO.getTypeId() == 8) {
                    String options = questionDO.getOptions();
                    if (StringUtil.isNotBlank(options)) {
                        List<OptionContentVO> optionContentVOS = QuestionContentUtil.optionConvert(options);
                        for (OptionContentVO optionContentVO : optionContentVOS) {
                            String content = optionContentVO.getContent();
                            if (StringUtil.isNotBlank(content)) {
                                optionContentVO.setContent(questionUrlPrefix + content);
                            }
                        }
                        questionDO.setOptions(JSON.toJSONString(optionContentVOS));
                    }
                }
                questionService.updateById(questionDO);
            }
        }
    }

    public void executeDataType2() {
        // 查询type_id=2的题
        QuestionPageReqVO questionPageReqVO = new QuestionPageReqVO();
        questionPageReqVO.setPageSize(5000);
        questionPageReqVO.setTypeId(2);
        PageResult<SourceQuestionDO> questionDOPageResult = sourceQuestionService.selectPage(questionPageReqVO);
        List<SourceQuestionDO> doPageResultList = questionDOPageResult.getList();
        for (SourceQuestionDO questionDO : doPageResultList) {
            String answer = questionDO.getAnswer();
            if (StringUtil.isNotEmpty(answer)) {
                List<AnswerContentVO> answerContentVOS = JSON.parseArray(answer, AnswerContentVO.class);
                List<QuestionDetailDO> questionDetailDOS = questionDetailService.selectListByQuestionId(questionDO.getId());
                QuestionDetailDO questionDetailDO = questionDetailDOS.get(0);
                List<OptionContentVO> optionContentVOList = new ArrayList<>();
                for (int i = 0; i < answerContentVOS.size(); i++) {
                    AnswerContentVO answerContentVO = answerContentVOS.get(i);
                    OptionContentVO optionContentVO = new OptionContentVO();
                    if (i == 0) {
                        optionContentVO.setKey("A");
                    } else if (i == 1) {
                        optionContentVO.setKey("B");
                    } else if (i == 2) {
                        optionContentVO.setKey("C");
                    } else if (i == 3) {
                        optionContentVO.setKey("D");
                    } else if (i == 4) {
                        optionContentVO.setKey("E");
                    } else if (i == 5) {
                        optionContentVO.setKey("F");
                    } else if (i == 6) {
                        optionContentVO.setKey("G");
                    }
                    Integer isAnswer = answerContentVO.getIs_answer();
                    String attachmentImage = answerContentVO.getAttachment_image();
                    optionContentVO.setIs_answer(isAnswer);
                    if (StringUtil.isNotBlank(attachmentImage)) {
                        optionContentVO.setContent(attachmentImage);
                    }
                    if (StringUtil.isNotBlank(answerContentVO.getContent())) {
                        optionContentVO.setContent(answerContentVO.getContent());
                    }
                    if (answerContentVO.getIs_answer() == 1) {
                        questionDetailDO.setAnswer(optionContentVO.getKey());
                    }
                    optionContentVOList.add(optionContentVO);
                }

                questionDetailDO.setOptions(JSON.toJSONString(optionContentVOList));
                questionDetailService.updateById(questionDetailDO);
            }

        }
    }

    public void executeDataType3() {
        // 查询type_id=2的题
        QuestionPageReqVO questionPageReqVO = new QuestionPageReqVO();
        questionPageReqVO.setPageSize(5000);
        questionPageReqVO.setTypeId(3);
        PageResult<SourceQuestionDO> questionDOPageResult = sourceQuestionService.selectPage(questionPageReqVO);
        List<SourceQuestionDO> doPageResultList = questionDOPageResult.getList();
        for (SourceQuestionDO questionDO : doPageResultList) {
            String answer = questionDO.getAnswer();
            if (StringUtil.isNotEmpty(answer)) {
                List<QuestionDetailDO> questionDetailDOS = questionDetailService.selectListByQuestionId(questionDO.getId());
                QuestionPageReqVO questionPageReqVOChi = new QuestionPageReqVO();
                questionPageReqVOChi.setParentId(questionDO.getId());
                questionPageReqVOChi.setPageSize(5000);
                PageResult<SourceQuestionDO> sourceQuestionChiResult = sourceQuestionService.selectPage(questionPageReqVOChi);
                List<SourceQuestionDO> sourceQuestionDOList = sourceQuestionChiResult.getList();
                if (sourceQuestionDOList.size() != questionDetailDOS.size()) {
                    log.info("异常数据，questionId：{}", questionDO.getId());
                    continue;
                }
                for (int i = 0; i < questionDetailDOS.size(); i++) {
                    QuestionDetailDO questionDetailDO = questionDetailDOS.get(i);
                    SourceQuestionDO sourceQuestionDO = sourceQuestionDOList.get(i);
                    String chiAnswer = sourceQuestionDO.getAnswer();
                    List<AnswerContentVO> answerContentVOS = JSON.parseArray(chiAnswer, AnswerContentVO.class);
                    for (int m = 0; m < answerContentVOS.size(); m++) {
                        AnswerContentVO answerContentVO = answerContentVOS.get(m);
                        questionDetailDO.setAnswer(answerContentVO.getText_answer());
                        if (answerContentVO.getAttachment_audio() != null) {
                            questionDetailDO.setAttachmentAudio(answerContentVO.getAttachment_audio());
                        }
                    }
                    questionDetailDO.setExplainTextCn(sourceQuestionDO.getExplainCn());

                    questionDetailService.updateById(questionDetailDO);

                }
            }

        }

    }
}