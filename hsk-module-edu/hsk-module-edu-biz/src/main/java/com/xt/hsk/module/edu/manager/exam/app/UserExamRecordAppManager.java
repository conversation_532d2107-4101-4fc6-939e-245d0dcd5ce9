package com.xt.hsk.module.edu.manager.exam.app;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.google.common.collect.Lists;
import com.xt.hsk.module.edu.controller.app.exam.vo.UserExamReportPageReqVO;
import com.xt.hsk.module.edu.controller.app.exam.vo.UserExamReportPageRespVO;
import com.xt.hsk.module.edu.controller.app.exam.vo.UserExamSubjectScoreRespVO;
import com.xt.hsk.module.edu.controller.app.exam.vo.UserExamUnitScoreRespVO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamDO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamDetailVersionDO;
import com.xt.hsk.module.edu.dal.dataobject.exam.UserExamRecordDO;
import com.xt.hsk.module.edu.dal.dataobject.exam.UserExamUnitScoreDO;
import com.xt.hsk.module.edu.enums.exam.ExamCorrectionStatusEnum;
import com.xt.hsk.module.edu.enums.exam.ExamRecordPracticeStatusEnum;
import com.xt.hsk.module.edu.service.exam.ExamDetailVersionService;
import com.xt.hsk.module.edu.service.exam.ExamService;
import com.xt.hsk.module.edu.service.exam.UserExamRecordService;
import com.xt.hsk.module.edu.service.exam.UserExamUnitScoreService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.edu.enums.ErrorCodeConstants.*;


/**
 * 用户模考记录 app Manager
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
@Component
public class UserExamRecordAppManager {

    @Resource
    private UserExamRecordService userExamRecordService;

    @Resource
    private ExamService examService;

    @Resource
    private ExamDetailVersionService examDetailVersionService;

    @Resource
    private UserExamUnitScoreService userExamUnitScoreService;

    /**
     * 获取用户模考报告页成绩
     *
     * @param reqVO 请求参数
     * @return 模考报告数据
     */
    public UserExamReportPageRespVO getExamReport(UserExamReportPageReqVO reqVO) {

        long userId = StpUtil.getLoginIdAsLong();

        // 查询模考记录
        UserExamRecordDO examRecord = userExamRecordService.getById(reqVO.getExamRecordId());
        if (examRecord == null || !Objects.equals(userId, examRecord.getUserId())) {
            throw exception(EXAM_RECORD_NOT_EXISTS);
        }

        if (!ExamRecordPracticeStatusEnum.COMPLETED.getCode().equals(examRecord.getPracticeStatus())) {
            throw exception(EXAM_RECORD_NOT_COMPLETED);
        }

        ExamDO exam = examService.getExam(examRecord.getExamId());

        Map<Integer, Map<Integer, String>> examDetailMap = examDetailVersionService.lambdaQuery()
                .select(ExamDetailVersionDO::getId,
                        ExamDetailVersionDO::getSubject,
                        ExamDetailVersionDO::getUnit,
                        ExamDetailVersionDO::getQuestionNames,
                        ExamDetailVersionDO::getQuestionCount)
                .eq(ExamDetailVersionDO::getExamId, exam.getId())
                .eq(ExamDetailVersionDO::getVersion, examRecord.getExamDetailVersion())
                .orderByAsc(ExamDetailVersionDO::getSubject, ExamDetailVersionDO::getUnit)
                .list()
                .stream()
                .collect(Collectors.groupingBy(
                        ExamDetailVersionDO::getSubject,
                        Collectors.toMap(
                                ExamDetailVersionDO::getUnit,
                                ExamDetailVersionDO::getQuestionNames,
                                (k1, k2) -> k2
                        )
                ));

        if (CollUtil.isEmpty(examDetailMap)) {
            throw exception(EXAM_NOT_EXISTS);
        }


        // 构建响应数据
        UserExamReportPageRespVO respVO = new UserExamReportPageRespVO();
        respVO.setTotalScore(examRecord.getTotalScore());
        respVO.setQuestionNum(examRecord.getQuestionNum());
        respVO.setAnsweredNum(examRecord.getAnswerNum());
        respVO.setAnswerTime(examRecord.getAnswerTime());
        respVO.setEndTime(examRecord.getEndTime());
        respVO.setSubjectScoreList(Lists.newArrayList());
        respVO.setExamId(exam.getId());
        respVO.setExamType(exam.getType());
        respVO.setExamSections(examRecord.getExamSections());
        respVO.setPracticeStatus(examRecord.getPracticeStatus());
        respVO.setCorrectionStatus(examRecord.getCorrectionStatus());

        Map<Integer, Map<Integer, BigDecimal>> subjectScoreMap = userExamUnitScoreService.lambdaQuery()
                .eq(UserExamUnitScoreDO::getExamId, examRecord.getExamId())
                .list()
                .stream()
                .collect(Collectors.groupingBy(
                        UserExamUnitScoreDO::getSubject,
                        Collectors.toMap(
                                UserExamUnitScoreDO::getUnit,
                                UserExamUnitScoreDO::getScore,
                                (k1, k2) -> k2
                        )
                ));

        List<UserExamSubjectScoreRespVO> subjectScoreList = new ArrayList<>();
        for (Map.Entry<Integer, Map<Integer, String>> examDetailBySubject : examDetailMap.entrySet()) {
            Integer subject = examDetailBySubject.getKey();
            Map<Integer, String> examDetailByUnitMap = examDetailBySubject.getValue();

            UserExamSubjectScoreRespVO subjectScoreVO = new UserExamSubjectScoreRespVO();
            subjectScoreVO.setSubject(subject);
            subjectScoreVO.setTotalScore(100);


            List<UserExamUnitScoreRespVO> unitScoreList = new ArrayList<>();

            int subjectActualScore = 0;

            Map<Integer, BigDecimal> unitScoreMap = subjectScoreMap.getOrDefault(subject, Map.of());

            for (Map.Entry<Integer, String> examDetailByUnit : examDetailByUnitMap.entrySet()) {
                Integer unit = examDetailByUnit.getKey();
                String questionNames = examDetailByUnit.getValue();
                int unitActualScore = unitScoreMap.getOrDefault(unit, BigDecimal.ZERO).intValue();

                UserExamUnitScoreRespVO unitScoreVO = new UserExamUnitScoreRespVO();
                unitScoreVO.setUnit(unit);
                unitScoreVO.setActualScore(unitActualScore);

                subjectActualScore += unitActualScore;

                List<String> questionNameList = new ArrayList<>();
                if (CharSequenceUtil.isNotBlank(questionNames)) {
                    questionNameList.addAll(Arrays.stream(questionNames.split(","))
                            .filter(CharSequenceUtil::isNotBlank)
                            .toList());
                }
                unitScoreVO.setQuestionNameList(questionNameList);
                unitScoreList.add(unitScoreVO);
            }

            subjectScoreVO.setUnitScoreList(unitScoreList);
            subjectScoreVO.setActualScore(subjectActualScore);
            subjectScoreList.add(subjectScoreVO);

        }

        respVO.setSubjectScoreList(subjectScoreList);

        if (ExamCorrectionStatusEnum.COMPLETED.getCode().equals(examRecord.getCorrectionStatus())) {
            respVO.setActualScore(examRecord.getActualScore());
            respVO.setCorrectNum(examRecord.getCorrectNum());
        }


        // 计算是否合格（得分≥总分*60%）
        boolean qualified = false;
        if (examRecord.getTotalScore() != null && examRecord.getActualScore() != null && examRecord.getTotalScore() > 0) {
            BigDecimal passScore = BigDecimal.valueOf(examRecord.getTotalScore())
                    .multiply(BigDecimal.valueOf(0.6))
                    .setScale(0, RoundingMode.UP);
            qualified = examRecord.getActualScore() >= passScore.intValue();
        }
        respVO.setQualified(qualified);
        return respVO;

    }
}