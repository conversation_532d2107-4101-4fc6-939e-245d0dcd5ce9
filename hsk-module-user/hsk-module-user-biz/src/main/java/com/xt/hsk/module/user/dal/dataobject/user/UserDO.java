package com.xt.hsk.module.user.dal.dataobject.user;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xt.hsk.framework.mybatis.core.dataobject.AppBaseDO;
import com.xt.hsk.module.user.enums.UserSourceEnum;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * HSK用户 DO
 *
 * <AUTHOR>
 */
@TableName("user")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDO extends AppBaseDO {

    /**
     * 用户ID
     */
    @TableId
    private Long id;

    /**
     * 用户昵称
     */
    private String nickname;
    /**
     * 头像
     */
    private String avatar;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 手机区号
     */
    private String countryCode;

    /**
     * 所属地区（国家）
     */
    private String country;

    /**
     * 当前HSK等级（1-6）
     */
    private Integer currentHskLevel;

    /**
     * 性别（0-未知 1-男 2-女）
     */
    private Integer gender;

    /**
     * 出生日期
     */
    private LocalDate birthDate;

    /**
     * 考试时间
     */
    private LocalDate examDate;

    /**
     * 密码（加密存储）
     */
    private String password;

    /**
     * 最近登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 最近登录IP（支持IPv6）
     */
    private String lastLoginIp;

    /**
     * 是否修改过密码（0-否 1-是）
     */
    private Boolean passwordChanged;

    /**
     * 是否已更新过初始信息（0-否 1-是）
     */
    private Boolean infoUpdated;

    /**
     * 职业（0-其他 1-上班族 2-大学生 3-中学生）
     */
    private Integer profession;

    /**
     * 学习汉语目的（0-其他 1-留学 2-旅游 3-职业发展 4-个人兴趣）
     */
    private Integer learningPurpose;

    /**
     * 汉语水平（0-不确定 1-第一次学中文 2-能简单对话 3-能用中文日常交流）
     */
    private Integer chineseLevel;

    /**
     * 目标HSK等级（1-6）
     */
    private Integer targetHskLevel;

    /**
     * 计划考试时间
     */
    private LocalDateTime plannedExamDate;

    /**
     * 用户来源（0-手动注册/管理员注册 1-APP注册 2-PC注册）
     *
     * @see UserSourceEnum
     */
    private Integer userSource;

    /**
     * 用户状态（0-启用 1-禁用）
     */
    private Integer status;

    /**
     * 删除时间（秒级时间戳）
     */
    private Long deleteTime;

} 