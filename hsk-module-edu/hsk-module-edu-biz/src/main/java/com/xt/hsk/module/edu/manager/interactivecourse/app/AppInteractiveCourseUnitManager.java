package com.xt.hsk.module.edu.manager.interactivecourse.app;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.google.common.base.Objects;
import com.xt.hsk.framework.common.enums.CommonStatusEnum;
import com.xt.hsk.framework.common.enums.IsShowEnum;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.BinaryUtils;
import com.xt.hsk.framework.common.util.i18n.LanguageUtils;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.content.CourseVideoLinkVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.content.CoursewareInfoVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseUnitPageReqVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.UnitVocabularyInfoVO;
import com.xt.hsk.module.edu.controller.app.interactivecourse.vo.AppInteractiveCourseUnitPageReqVO;
import com.xt.hsk.module.edu.controller.app.interactivecourse.vo.AppInteractiveCourseUnitReqVO;
import com.xt.hsk.module.edu.controller.app.interactivecourse.vo.InteractiveCourseUnitAppDetailsVO;
import com.xt.hsk.module.edu.controller.app.interactivecourse.vo.InteractiveCourseUnitAppRespVO;
import com.xt.hsk.module.edu.controller.app.interactivecourse.vo.NextUnitInfoRespVO;
import com.xt.hsk.module.edu.controller.app.interactivecourse.vo.UpdateRecordSaveOptionReqVO;
import com.xt.hsk.module.edu.controller.app.interactivecourse.vo.VideoRecordSaveVO;
import com.xt.hsk.module.edu.convert.interactivecourse.InteractiveCourseUnitConvert;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.InteractiveCourseCoursewareDO;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.InteractiveCourseUnitDO;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.InteractiveCourseUnitResourceRelDO;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.UserInteractiveCourseRecordDO;
import com.xt.hsk.module.edu.dal.dataobject.question.QuestionDO;
import com.xt.hsk.module.edu.dal.dataobject.word.WordDO;
import com.xt.hsk.module.edu.dal.dataobject.word.WordMeaningDO;
import com.xt.hsk.module.edu.enums.LanguageEnum;
import com.xt.hsk.module.edu.enums.interactivecourse.InteractiveCourseRecordBizTypeEnum;
import com.xt.hsk.module.edu.enums.interactivecourse.InteractiveCourseStatusEnum;
import com.xt.hsk.module.edu.enums.interactivecourse.UnitQuestionSourceTypeEnum;
import com.xt.hsk.module.edu.enums.interactivecourse.UnitResourceTypeEnum;
import com.xt.hsk.module.edu.enums.interactivecourse.VideoTypeEnum;
import com.xt.hsk.module.edu.service.interactivecourse.InteractiveCourseCoursewareService;
import com.xt.hsk.module.edu.service.interactivecourse.InteractiveCourseUnitResourceRelService;
import com.xt.hsk.module.edu.service.interactivecourse.InteractiveCourseUnitService;
import com.xt.hsk.module.edu.service.interactivecourse.UserInteractiveCourseRecordService;
import com.xt.hsk.module.edu.service.question.QuestionService;
import com.xt.hsk.module.edu.service.word.WordMeaningService;
import com.xt.hsk.module.edu.service.word.WordService;
import com.xt.hsk.module.game.api.SpecialExerciseApi;
import com.xt.hsk.module.game.api.dto.SpecialExercisePageRespDTO;
import com.xt.hsk.module.thirdparty.api.video.VideoApi;
import com.xt.hsk.module.thirdparty.api.video.dto.VideoRespDTO;
import com.xt.hsk.module.thirdparty.enums.VideoQualityEnum;
import com.xt.hsk.module.thirdparty.enums.VideoSourceEnum;
import com.xt.hsk.module.user.enums.FavoriteTypeEnum;
import com.xt.hsk.module.user.favorite.UserFavoriteApi;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * App 互动课程单元管理
 *
 * <AUTHOR>
 * @since 2025/06/12
 */
@Slf4j
@Component
public class AppInteractiveCourseUnitManager {

    /**
     * 互动课程单元服务
     */
    @Resource
    private InteractiveCourseUnitService interactiveCourseUnitService;
    /**
     * 词汇服务
     */
    @Resource
    private WordService wordService;
    /**
     * 课程课件服务
     */
    @Resource
    private InteractiveCourseCoursewareService coursewareService;
    /**
     * 互动课程单元资源关系服务
     */
    @Resource
    private InteractiveCourseUnitResourceRelService unitResourceRelService;
    /**
     * 互动课程记录服务
     */
    @Resource
    private UserInteractiveCourseRecordService userInteractiveCourseRecordService;

    /**
     * 词义服务
     */
    @Resource
    private WordMeaningService wordMeaningService;

    /**
     * 题目服务
     */
    @Resource
    private QuestionService questionService;

    /**
     * 专项练习API
     */
    @Resource
    private SpecialExerciseApi specialExerciseApi;

    /**
     * 视频API
     */
    @Resource
    private VideoApi videoApi;

    /**
     * 用户收藏 API
     */
    @Resource
    private UserFavoriteApi userFavoriteApi;

    public PageResult<InteractiveCourseUnitAppRespVO> getInteractiveCourseUnitPage(
        @Valid AppInteractiveCourseUnitPageReqVO reqVO) {

        InteractiveCourseUnitPageReqVO pageReqVO = new InteractiveCourseUnitPageReqVO();
        pageReqVO.setCourseId(reqVO.getCourseId());
        pageReqVO.setDisplayStatus(true);
        pageReqVO.setPageNo(reqVO.getPageNo());
        pageReqVO.setPageSize(reqVO.getPageSize());

        PageResult<InteractiveCourseUnitDO> unitPage = interactiveCourseUnitService.getInteractiveCourseUnitPage(
            pageReqVO);
        if (CollUtil.isEmpty(unitPage.getList())) {
            return PageResult.empty();
        }

        List<InteractiveCourseUnitAppRespVO> unitAppRespVOList = InteractiveCourseUnitConvert.INSTANCE.doListToAppPageVOList(
            unitPage.getList());

        // 填充专项练习ID和真题练习ID
        fillResourceIds(unitAppRespVOList);

        // 获取上次练习记录
        setLastPracticeRecordInfo(unitAppRespVOList);

        return new PageResult<>(unitAppRespVOList, unitPage.getTotal());
    }

    /**
     * 获取互动课单元上次记录信息
     */
    private void setLastPracticeRecordInfo(List<InteractiveCourseUnitAppRespVO> unitAppRespVOList) {
        // 如果未登录或单元列表为空，直接返回
        if (!StpUtil.isLogin() || CollUtil.isEmpty(unitAppRespVOList)) {
            return;
        }

        // 获取用户上次练习记录
        Long currentUserId = StpUtil.getLoginIdAsLong();
        List<Long> unitIds = unitAppRespVOList.stream()
            .map(InteractiveCourseUnitAppRespVO::getId)
            .toList();

        Map<Long, UserInteractiveCourseRecordDO> unitRecordMap =
            userInteractiveCourseRecordService.getLatestRecordByUnitIds(currentUserId, unitIds);

        // 批量查询视频类型单元的完成状态
        List<Long> videoUnitIds = unitAppRespVOList.stream()
            .filter(unitVO -> UnitQuestionSourceTypeEnum.VIDEO.getCode().equals(unitVO.getQuestionSource()))
            .map(InteractiveCourseUnitAppRespVO::getId)
            .toList();

        Map<Long, Integer> videoCompletionVersionMap = userInteractiveCourseRecordService
            .batchGetVideoCompletionResourceVersion(currentUserId, videoUnitIds);

        // 为每个单元设置上次练习记录信息
        for (InteractiveCourseUnitAppRespVO unitVO : unitAppRespVOList) {
            UserInteractiveCourseRecordDO recordDO = unitRecordMap.get(unitVO.getId());

            // 检查记录是否存在且资源版本是否匹配
            if (recordDO != null && isResourceVersionMatched(recordDO.getResourceVersion(), unitVO.getResourceVersion())) {
                // 设置上次练习记录信息
                unitVO.setLastPracticeRecordInfo(
                    InteractiveCourseUnitConvert.INSTANCE.recordToLastRecordInfoVO(recordDO));

                // 针对视频类型单元的特殊处理
                if (UnitQuestionSourceTypeEnum.VIDEO.getCode().equals(unitVO.getQuestionSource())) {
                    // 视频类型：检查是否曾经完成过视频观看且资源版本匹配
                    Integer completedResourceVersion = videoCompletionVersionMap.get(unitVO.getId());

                    if (completedResourceVersion != null && completedResourceVersion.equals(unitVO.getResourceVersion())) {
                        // 如果曾经看完过且资源版本匹配，学习状态设为已完成
                        unitVO.setLearningStatus(InteractiveCourseStatusEnum.COMPLETED.getCode());
                    } else {
                        // 如果没有看完过或资源版本不匹配（后台换了视频），使用最新记录的状态
                        unitVO.setLearningStatus(recordDO.getStatus());
                    }
                } else {
                    // 非视频类型：使用最新记录的状态
                    unitVO.setLearningStatus(recordDO.getStatus());
                }
            }
        }
    }

    /**
     * 检查资源版本是否匹配
     */
    private boolean isResourceVersionMatched(Integer recordVersion, Integer unitVersion) {
        return Objects.equal(recordVersion, unitVersion);
    }

    /**
     * 获取交互式课程单元详细信息
     *
     * @param reqVO req vo
     * @return {@code InteractiveCourseUnitAppRespVO }
     */
    public InteractiveCourseUnitAppDetailsVO getInteractiveCourseUnitDetail(
        @Valid AppInteractiveCourseUnitReqVO reqVO) {
        // 1. 根据单元ID查询单元信息
        InteractiveCourseUnitDO unitDO = interactiveCourseUnitService.getById(reqVO.getUnitId());
        if (unitDO == null) {
            return null;
        }

        // 2. 转换为APP响应VO
        InteractiveCourseUnitAppDetailsVO respVO = InteractiveCourseUnitConvert.INSTANCE.doToAppRespVO(unitDO);

        // 3. 获取并设置视频信息
        enrichVideoInfo(reqVO.getUnitId(), unitDO.getVideoId(), respVO);

        // =================================查询用户上一次课程观看记录==============================
        UserInteractiveCourseRecordDO userInteractiveCourseRecordDO = userInteractiveCourseRecordService
            .getInteractiveCourseRecord(unitDO.getId(), StpUtil.getLoginIdAsLong(),
                InteractiveCourseRecordBizTypeEnum.VIDEO_VIEWING_RECORD);

        respVO.setLastVideoRecordInfo(InteractiveCourseUnitConvert.INSTANCE
            .recordToLastVideoRecordInfoVO(userInteractiveCourseRecordDO));
        // 是否曾经完播
        respVO.setHasEverCompleted(userInteractiveCourseRecordService.checkVideoCompletion(
            unitDO.getId(),
            StpUtil.getLoginIdAsLong(),
            unitDO.getResourceVersion()));
        // =================================查询用户上一次课程观看记录==============================

        // =================================查询下一节课程单元ID==============================
        Long nextUnitId = interactiveCourseUnitService.getNextUnitId(unitDO.getCourseId(), unitDO.getSort());
        respVO.setNextUnitId(nextUnitId);
        // =================================查询下一节课程单元ID==============================

        return respVO;
    }

    /**
     * 丰富视频信息
     *
     * @param unitId 单元ID
     * @param videoId 视频信息ID
     * @param respVO 响应对象
     */
    private void enrichVideoInfo(Long unitId, String videoId, InteractiveCourseUnitAppDetailsVO respVO) {
        List<VideoRespDTO> respDTOList = videoApi.getByAssetIdAndSource(videoId,
            VideoSourceEnum.INTERACTIVE_COURSE.getCode());
        if (CollUtil.isEmpty(respDTOList)) {
            return;
        }

        // 设置基本视频信息
        respVO.setVideoLinkList(parseVideoLinks(respDTOList));

        // 根据视频类型处理不同资源
        Integer videoType = respVO.getVideoType();
        if (VideoTypeEnum.COURSEWARE.getCode().equals(videoType)) {
            enrichCoursewareInfo(unitId, respVO);
        } else if (VideoTypeEnum.VOCABULARY.getCode().equals(videoType)) {
            enrichVocabularyInfo(unitId, respVO);
        }
    }

    /**
     * 解析视频链接JSON
     */
    private List<CourseVideoLinkVO> parseVideoLinks(List<VideoRespDTO> videoRespDTOList) {
        if (CollUtil.isEmpty(videoRespDTOList)) {
            return Collections.emptyList();
        }
        return videoRespDTOList.stream()
            .map(video -> {
                CourseVideoLinkVO linkVO = new CourseVideoLinkVO();
                linkVO.setUrl(video.getVideoUrl());
                linkVO.setResolution(VideoQualityEnum.getResolutionByCode(video.getQuality()));
                return linkVO;
            })
            .toList();
    }

    /**
     * 丰富课件信息
     *
     * @param unitId 单元ID
     * @param respVO 响应对象
     */
    private void enrichCoursewareInfo(Long unitId, InteractiveCourseUnitAppDetailsVO respVO) {
        List<InteractiveCourseUnitResourceRelDO> resourceRelDOList = getUnitResources(
            unitId, UnitResourceTypeEnum.COURSEWARE.getCode());

        if (CollUtil.isEmpty(resourceRelDOList)) {
            return;
        }

        List<Long> coursewareIds = resourceRelDOList.stream()
            .map(InteractiveCourseUnitResourceRelDO::getResourceId)
            .toList();

        // 创建ID到排序序号的映射
        Map<Long, Integer> sortMap = resourceRelDOList.stream()
            .collect(Collectors.toMap(
                InteractiveCourseUnitResourceRelDO::getResourceId,
                InteractiveCourseUnitResourceRelDO::getSort
            ));

        List<InteractiveCourseCoursewareDO> coursewareDOList = coursewareService.listByIds(coursewareIds);

        respVO.setCoursewareList(coursewareDOList.stream()
            .map(courseware -> convertToCoursewareInfoVO(courseware, sortMap.get(courseware.getId())))
            .sorted(Comparator.comparing(CoursewareInfoVO::getSort, Comparator.nullsLast(Integer::compareTo)))
            .toList());
    }

    /**
     * 转换为课件信息VO
     *
     * @param coursewareDO 课件DO
     * @param sort 排序序号（从资源关联表获取）
     * @return 课件信息VO
     */
    private CoursewareInfoVO convertToCoursewareInfoVO(InteractiveCourseCoursewareDO coursewareDO, Integer sort) {
        CoursewareInfoVO coursewareInfoVO = new CoursewareInfoVO();
        coursewareInfoVO.setMaterialName(coursewareDO.getMaterialName());
        coursewareInfoVO.setSort(sort != null ? sort : coursewareDO.getSort());
        coursewareInfoVO.setMaterialUrl(coursewareDO.getMaterialUrl());
        coursewareInfoVO.setMaterialSize(coursewareDO.getMaterialSize());
        return coursewareInfoVO;
    }


    /**
     * 丰富词汇信息
     *
     * @param unitId 单元ID
     * @param respVO 响应对象
     */
    private void enrichVocabularyInfo(Long unitId, InteractiveCourseUnitAppDetailsVO respVO) {
        List<InteractiveCourseUnitResourceRelDO> resourceRelDOList = getUnitResources(
            unitId, UnitResourceTypeEnum.VOCABULARY.getCode());
            
        if (CollUtil.isEmpty(resourceRelDOList)) {
            return;
        }

        List<Long> wordIds = resourceRelDOList.stream()
            .map(InteractiveCourseUnitResourceRelDO::getResourceId)
            .toList();

        Map<Long, Integer> sortMap = resourceRelDOList.stream()
            .collect(Collectors.toMap(
                InteractiveCourseUnitResourceRelDO::getResourceId,
                InteractiveCourseUnitResourceRelDO::getSort
            ));

        List<WordDO> wordDOList = wordService.lambdaQuery()
            .in(WordDO::getId, wordIds)
            .list();
            
        List<UnitVocabularyInfoVO> vocabularyList = wordDOList.stream()
            .map(word -> convertToVocabularyInfoVO(word, sortMap.get(word.getId())))
            .sorted(Comparator.comparing(UnitVocabularyInfoVO::getSort))
            .toList();

        // 含义
        String language = LanguageUtils.getLanguage();
        Map<Long, WordMeaningDO> wordMeaningMap = wordMeaningService.getByWordIds(wordIds);
        vocabularyList.forEach(vocabularyInfoVO -> {
            WordMeaningDO wordMeaningDO = wordMeaningMap.get(vocabularyInfoVO.getWordId());
            // 越南语或者中文环境时 返回越南语含义 英文环境时返回英文含义
            if (wordMeaningDO != null) {
                if (LanguageEnum.LANGUAGE_CN.getCode().equals(language)) {
                    vocabularyInfoVO.setInterpretation(wordMeaningDO.getTranslationOt());
                }
                if (LanguageEnum.LANGUAGE_EN.getCode().equals(language)) {
                    vocabularyInfoVO.setInterpretation(wordMeaningDO.getTranslationOt());
                }
                if (LanguageEnum.LANGUAGE_VI.getCode().equals(language)) {
                    vocabularyInfoVO.setInterpretation(wordMeaningDO.getTranslationEn());
                }
            }
        });


        // =============================== 字词收藏状态查询 ================================
        Map<Long, Boolean> selectedStatus = userFavoriteApi.selectStatus(unitId, wordIds,
            FavoriteTypeEnum.VOCABULARY.getCode());
        vocabularyList.forEach(vocabularyInfoVO ->
            vocabularyInfoVO.setCollect(selectedStatus.getOrDefault(vocabularyInfoVO.getWordId(), false)));
        // =============================== 字词收藏状态查询 ================================
        respVO.setVocabularyList(vocabularyList);
    }

    /**
     * 转换为词汇信息VO
     *
     * @param word 词汇DO
     * @param sort 排序
     * @return 词汇信息VO
     */
    private UnitVocabularyInfoVO convertToVocabularyInfoVO(WordDO word, Integer sort) {
        UnitVocabularyInfoVO vocabularyInfoVO = new UnitVocabularyInfoVO();
        vocabularyInfoVO.setSort(sort);
        vocabularyInfoVO.setWordId(word.getId());
        vocabularyInfoVO.setAudioUrl(word.getAudioUrl());

        // 处理原始单词，移除可能的多余空格
        if (StringUtils.isNotBlank(word.getWord())) {
            String originalWord = word.getWord();
            String cleanedWord = originalWord.replaceAll("\\s+", "");
            vocabularyInfoVO.setWord(cleanedWord);

            // 将单词正确分割为单字列表（处理中文字符）
            List<String> wordList = cleanedWord.codePoints()
                .mapToObj(c -> new String(Character.toChars(c)))
                .toList();
            vocabularyInfoVO.setWordList(wordList);
        }

        if (StringUtils.isNotBlank(word.getPinyin())) {
            // 将拼音转为数组
            vocabularyInfoVO.setPinyinList(Arrays.stream(StringUtils.split(
                word.getPinyin(), " ")).toList());
        }
        
        vocabularyInfoVO.setPinyin(word.getPinyin());
        vocabularyInfoVO.setIsSpecial(word.getIsSpecial());
        vocabularyInfoVO.setHskLevel(word.getHskLevel());
        // 获取等级
        List<Integer> hskLevels = BinaryUtils.getBinaryBitValues(word.getHskLevel());
        vocabularyInfoVO.setHskLevelList(hskLevels);

        return vocabularyInfoVO;
    }

    /**
     * 获取单元资源关系列表
     *
     * @param unitId 单元ID
     * @param resourceType 资源类型
     * @return 单元资源关系列表
     */
    private List<InteractiveCourseUnitResourceRelDO> getUnitResources(Long unitId, Integer resourceType) {
        return unitResourceRelService.lambdaQuery()
            .eq(InteractiveCourseUnitResourceRelDO::getUnitId, unitId)
            .eq(InteractiveCourseUnitResourceRelDO::getResourceType, resourceType)
            .orderByAsc(InteractiveCourseUnitResourceRelDO::getSort)
            .list();
    }

    /**
     * 记录视频观看
     * <p>
     *     如果ID存在，则更新；否则插入
     * @param reqVO 请求对象
     */
    @Transactional(rollbackFor = Exception.class)
    public void recordVideoWatch(@Valid VideoRecordSaveVO reqVO) {
        // 1. 参数校验
        if (reqVO.getVideoDuration() == null || reqVO.getVideoDuration() <= 0) {
            log.warn("视频时长无效,不进行记录: videoDuration = {},viewingDuration = {}",
                reqVO.getVideoDuration(), reqVO.getViewingDuration());
            return;
        }

        // 2. 校验单元存在性
        InteractiveCourseUnitDO unitDO = interactiveCourseUnitService.getById(reqVO.getUnitId());
        if (unitDO == null) {
            log.warn("互动课单元不存在: unitId = {}", reqVO.getUnitId());
            return;
        }

        // 3. 计算观看进度
        BigDecimal videoProgress = calculateViewingProgress(reqVO.getViewingDuration(), reqVO.getVideoDuration());

        // 4. 处理记录
        Long videoRecordId = reqVO.getVideoRecordId();
        Long currentUserId = StpUtil.getLoginIdAsLong();

        if (videoRecordId != null) {
            // 4.1 更新现有记录
            updateExistingRecord(videoRecordId, currentUserId, reqVO, videoProgress);
        } else {
            // 4.2 创建新记录
            createNewRecord(reqVO, unitDO, currentUserId, videoProgress);
        }
    }

    /**
     * 更新现有视频观看记录
     *
     * @param videoRecordId 视频记录ID
     * @param currentUserId 当前用户ID
     * @param reqVO 请求对象
     * @param videoProgress 计算好的视频进度
     */
    private void updateExistingRecord(Long videoRecordId, Long currentUserId, VideoRecordSaveVO reqVO, BigDecimal videoProgress) {
        UserInteractiveCourseRecordDO recordDO = userInteractiveCourseRecordService.getById(videoRecordId);
        
        // 记录不存在
        if (recordDO == null) {
            log.warn("视频观看记录不存在: videoRecordId = {}", videoRecordId);
            return;
        }

        // 权限校验：确保用户只能更新自己的记录
        if (!recordDO.getUserId().equals(currentUserId)) {
            log.warn("用户无权更新此记录: userId = {}, recordUserId = {}", currentUserId, recordDO.getUserId());
            return;
        }

        // 完成状态判断 完成率 = 100% 时 算完成 其他都算进行中
        InteractiveCourseStatusEnum status = videoProgress.compareTo(BigDecimal.valueOf(100)) == 0 ?
            InteractiveCourseStatusEnum.COMPLETED : InteractiveCourseStatusEnum.IN_PROGRESS;

        // 更新记录
        recordDO.setVideoDuration(reqVO.getVideoDuration());
        recordDO.setViewingDuration(reqVO.getViewingDuration());
        recordDO.setVideoProgress(videoProgress);
        recordDO.setStatus(status.getCode());
        recordDO.setSaveOption(reqVO.getSaveOption());
        
        boolean updated = userInteractiveCourseRecordService.updateById(recordDO);
        if (!updated) {
            log.warn("更新视频记录失败: recordId = {}", videoRecordId);
        }
    }

    /**
     * 创建新的视频观看记录
     *
     * @param reqVO 请求对象
     * @param unitDO 单元DO
     * @param userId 用户ID
     * @param videoProgress 计算好的视频进度
     */
    private void createNewRecord(VideoRecordSaveVO reqVO, InteractiveCourseUnitDO unitDO, Long userId, BigDecimal videoProgress) {
        userInteractiveCourseRecordService.createVideoRecord(userId,
            unitDO.getCourseId(),
            reqVO.getUnitId(),
            reqVO.getVideoDuration(),
            reqVO.getViewingDuration(),
            reqVO.getSaveOption(),
            videoProgress);
    }

    /**
     * 计算观看进度 = 视频观看时长 / 视频总时长 * 100 保留两位小数
     *
     * @param viewingDuration 观看时长
     * @param videoDuration   视频时长
     * @return double
     */
    private BigDecimal calculateViewingProgress(Integer viewingDuration, Integer videoDuration) {
        if (videoDuration == 0 || viewingDuration == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal div = NumberUtil.div(viewingDuration, videoDuration, 2);
        BigDecimal progress = NumberUtil.mul(div, new BigDecimal("100"));
        // 使用 min 限制最大值为 100
        progress = progress.min(new BigDecimal("100"));
        return progress;
    }

    /**
     * 获取下一个单元信息
     *
     * @param reqVO 请求参数
     * @return 下一个单元信息
     */
    public NextUnitInfoRespVO getNextUnitInfo(@Valid AppInteractiveCourseUnitReqVO reqVO) {
        // 1. 根据当前单元ID查询单元信息
        InteractiveCourseUnitDO currentUnit = interactiveCourseUnitService.getById(reqVO.getUnitId());
        if (currentUnit == null) {
            return null;
        }

        // 2. 查询下一个单元ID
        Long nextUnitId = interactiveCourseUnitService.getNextUnitId(currentUnit.getCourseId(), currentUnit.getSort());
        if (nextUnitId == null) {
            return null;
        }

        // 3. 查询下一个单元的详细信息
        InteractiveCourseUnitDO nextUnit = interactiveCourseUnitService.getById(nextUnitId);
        if (nextUnit == null) {
            return null;
        }

        // 4. 构建响应对象
        NextUnitInfoRespVO respVO = new NextUnitInfoRespVO();
        respVO.setNextUnitId(nextUnitId);
        respVO.setCurrentUnitId(currentUnit.getId());
        respVO.setQuestionSource(nextUnit.getQuestionSource());

        // 5. 根据单元类型设置相应的资源信息
        if (UnitQuestionSourceTypeEnum.SPECIAL_PRACTICE.getCode().equals(nextUnit.getQuestionSource())) {
            // 专项练习：获取专项练习ID
            setSpecialExerciseInfo(nextUnitId, respVO);
        } else if (UnitQuestionSourceTypeEnum.QUESTION.getCode().equals(nextUnit.getQuestionSource())) {
            // 真题练习：获取题目ID列表（只包含未禁用的题目）
            setQuestionInfo(nextUnitId, respVO);
        } else if (UnitQuestionSourceTypeEnum.VIDEO.getCode()
            .equals(nextUnit.getQuestionSource())) {
            // 视频：获取视频ID
            respVO.setVideoInfoId(nextUnit.getVideoId());
        }

        if (StpUtil.isLogin()) {
            Map<Long, UserInteractiveCourseRecordDO> unitRecordMap =
                userInteractiveCourseRecordService.getLatestRecordByUnitIds(StpUtil.getLoginIdAsLong(),
                    Collections.singletonList(nextUnitId));
            UserInteractiveCourseRecordDO recordDO = unitRecordMap.get(nextUnitId);

            // 检查记录是否存在且资源版本是否匹配
            if (recordDO != null && isResourceVersionMatched(recordDO.getResourceVersion(), nextUnit.getResourceVersion())) {
                // 设置上次练习记录信息和学习状态
                respVO.setLastPracticeRecordInfo(
                    InteractiveCourseUnitConvert.INSTANCE.recordToLastRecordInfoVO(recordDO));
                respVO.setLearningStatus(recordDO.getStatus());
            }
        }

        return respVO;
    }

    /**
     * 设置专项练习信息
     */
    private void setSpecialExerciseInfo(Long unitId, NextUnitInfoRespVO respVO) {
        List<InteractiveCourseUnitResourceRelDO> resourceRels = unitResourceRelService.lambdaQuery()
            .eq(InteractiveCourseUnitResourceRelDO::getUnitId, unitId)
            .eq(InteractiveCourseUnitResourceRelDO::getResourceType, UnitResourceTypeEnum.SPECIAL_PRACTICE.getCode())
            .orderByAsc(InteractiveCourseUnitResourceRelDO::getSort)
            .list();

        if (CollUtil.isNotEmpty(resourceRels)) {
            // 取第一个专项练习ID
            SpecialExercisePageRespDTO questionInfo = specialExerciseApi.getQuestionInfo(
                resourceRels.get(0).getResourceId());
            if (questionInfo == null) {
                log.warn("专项练习不存在: specialExerciseId = {}", resourceRels.get(0).getResourceId());
                return;
            }
            if (IsShowEnum.DISPLAY.getCode().equals(questionInfo.getIsShow())) {
                log.warn("专项练习已隐藏: specialExerciseId = {}", resourceRels.get(0).getResourceId());
                return;
            }
            respVO.setSpecialExerciseId(questionInfo.getSpecialExerciseId());
            respVO.setSpecialPracticeType(questionInfo.getType());

        }
    }

    /**
     * 设置真题练习信息
     */
    private void setQuestionInfo(Long unitId, NextUnitInfoRespVO respVO) {
        List<InteractiveCourseUnitResourceRelDO> resourceRels = unitResourceRelService.lambdaQuery()
            .eq(InteractiveCourseUnitResourceRelDO::getUnitId, unitId)
            .eq(InteractiveCourseUnitResourceRelDO::getResourceType, UnitResourceTypeEnum.QUESTION.getCode())
            .orderByAsc(InteractiveCourseUnitResourceRelDO::getSort)
            .list();

        if (CollUtil.isNotEmpty(resourceRels)) {
            // 获取题目ID列表（注意：这里的resourceId直接就是题目ID，不是题型ID）
            List<Long> questionIds = resourceRels.stream()
                .map(InteractiveCourseUnitResourceRelDO::getResourceId)
                .toList();

            // 过滤出未禁用的题目ID
            List<QuestionDO> questions = questionService.lambdaQuery()
                .in(QuestionDO::getId, questionIds)
                .eq(QuestionDO::getIsShow, IsShowEnum.SHOW.getCode())
                .eq(QuestionDO::getStatus, CommonStatusEnum.ENABLE.getStatus())
                .select(QuestionDO::getId)
                .list();

            if (CollUtil.isNotEmpty(questions)) {
                List<Long> enabledQuestionIds = questions.stream()
                    .map(QuestionDO::getId)
                    .toList();
                respVO.setQuestionIds(enabledQuestionIds);
            }
        }
    }

    /**
     * 填充专项练习ID和真题练习ID
     */
    private void fillResourceIds(List<InteractiveCourseUnitAppRespVO> unitAppRespVOList) {
        if (CollUtil.isEmpty(unitAppRespVOList)) {
            return;
        }

        // 按单元类型分组处理
        Map<Integer, List<InteractiveCourseUnitAppRespVO>> unitsByType = unitAppRespVOList.stream()
            .collect(Collectors.groupingBy(InteractiveCourseUnitAppRespVO::getQuestionSource));

        // 处理专项练习类型单元
        List<InteractiveCourseUnitAppRespVO> specialPracticeUnits = unitsByType.get(
            UnitQuestionSourceTypeEnum.SPECIAL_PRACTICE.getCode());
        if (CollUtil.isNotEmpty(specialPracticeUnits)) {
            fillSpecialPracticeIds(specialPracticeUnits);
        }

        // 处理真题练习类型单元
        List<InteractiveCourseUnitAppRespVO> questionUnits = unitsByType.get(
            UnitQuestionSourceTypeEnum.QUESTION.getCode());
        if (CollUtil.isNotEmpty(questionUnits)) {
            fillQuestionIds(questionUnits);
        }
    }

    /**
     * 填充专项练习ID
     */
    private void fillSpecialPracticeIds(List<InteractiveCourseUnitAppRespVO> specialPracticeUnits) {
        List<Long> unitIds = specialPracticeUnits.stream()
            .map(InteractiveCourseUnitAppRespVO::getId)
            .toList();

        // 查询专项练习关联关系
        List<InteractiveCourseUnitResourceRelDO> resourceRels = unitResourceRelService.lambdaQuery()
            .in(InteractiveCourseUnitResourceRelDO::getUnitId, unitIds)
            .eq(InteractiveCourseUnitResourceRelDO::getResourceType, UnitResourceTypeEnum.SPECIAL_PRACTICE.getCode())
            .orderByAsc(InteractiveCourseUnitResourceRelDO::getSort)
            .list();
        if (CollUtil.isEmpty(resourceRels)) {
            return;
        }

        List<SpecialExercisePageRespDTO> questionInfoList = specialExerciseApi.getQuestionInfoList(
            resourceRels.stream()
                .map(InteractiveCourseUnitResourceRelDO::getResourceId)
                .toList());
        Map<Long, SpecialExercisePageRespDTO> gameMap = questionInfoList.stream()
            .collect(Collectors.toMap(SpecialExercisePageRespDTO::getId, Function.identity()));

        // 按单元ID分组
        Map<Long, List<InteractiveCourseUnitResourceRelDO>> relsByUnitId = resourceRels.stream()
            .collect(Collectors.groupingBy(InteractiveCourseUnitResourceRelDO::getUnitId));

        // 填充专项练习ID
        for (InteractiveCourseUnitAppRespVO unit : specialPracticeUnits) {
            List<InteractiveCourseUnitResourceRelDO> unitRels = relsByUnitId.get(unit.getId());
            if (CollUtil.isNotEmpty(unitRels)) {
                List<Long> practiceIds = unitRels.stream()
                    .map(InteractiveCourseUnitResourceRelDO::getResourceId)
                    .toList();
                SpecialExercisePageRespDTO pageRespDTO = gameMap.get(
                    practiceIds.get(0));
                unit.setSpecialPracticeId(
                    Optional.ofNullable(pageRespDTO).map(SpecialExercisePageRespDTO::getId).orElse(null));
                unit.setSpecialPracticeType(
                    Optional.ofNullable(pageRespDTO).map(SpecialExercisePageRespDTO::getType).orElse(null));
            }
        }
    }

    /**
     * 填充真题练习ID
     */
    private void fillQuestionIds(List<InteractiveCourseUnitAppRespVO> questionUnits) {
        List<Long> unitIds = questionUnits.stream()
            .map(InteractiveCourseUnitAppRespVO::getId)
            .toList();

        // 查询真题练习关联关系
        List<InteractiveCourseUnitResourceRelDO> resourceRels = unitResourceRelService.lambdaQuery()
            .in(InteractiveCourseUnitResourceRelDO::getUnitId, unitIds)
            .eq(InteractiveCourseUnitResourceRelDO::getResourceType, UnitResourceTypeEnum.QUESTION.getCode())
            .orderByAsc(InteractiveCourseUnitResourceRelDO::getSort)
            .list();

        // 按单元ID分组
        Map<Long, List<InteractiveCourseUnitResourceRelDO>> relsByUnitId = resourceRels.stream()
            .collect(Collectors.groupingBy(InteractiveCourseUnitResourceRelDO::getUnitId));

        // 填充真题练习ID
        for (InteractiveCourseUnitAppRespVO unit : questionUnits) {
            List<InteractiveCourseUnitResourceRelDO> unitRels = relsByUnitId.get(unit.getId());
            if (CollUtil.isNotEmpty(unitRels)) {
                List<Long> questionIds = unitRels.stream()
                    .map(InteractiveCourseUnitResourceRelDO::getResourceId)
                    .toList();
                unit.setQuestionIds(questionIds);
            }
        }
    }

    /**
     * 更新记录保存选项
     *
     * @param reqVO 请求参数
     */
    public void updateRecordSaveOption(@Valid UpdateRecordSaveOptionReqVO reqVO) {
        Long currentUserId = StpUtil.getLoginIdAsLong();

        // 1. 通过unitId和bizId查找当前用户的记录
        UserInteractiveCourseRecordDO recordDO = userInteractiveCourseRecordService.lambdaQuery()
            .eq(UserInteractiveCourseRecordDO::getUserId, currentUserId)
            .eq(UserInteractiveCourseRecordDO::getBizType, reqVO.getBizType())
            .eq(UserInteractiveCourseRecordDO::getUnitId, reqVO.getUnitId())
            .eq(UserInteractiveCourseRecordDO::getBizId, reqVO.getBizId())
            .orderByDesc(UserInteractiveCourseRecordDO::getId)
            .last("LIMIT 1")
            .one();

        if (recordDO == null) {
            log.error("[recordVideoWatch][用户({}) 单元({}) 业务({}) 记录不存在]", currentUserId , reqVO.getUnitId(), reqVO.getBizId());
            return;
        }

        // 2. 更新保存选项
        recordDO.setSaveOption(reqVO.getSaveOption());
        recordDO.setUpdateTime(LocalDateTime.now());
        userInteractiveCourseRecordService.updateById(recordDO);
    }

    /**
     * 获取单元信息
     *
     * @param reqVO 请求参数
     * @return 真题练习信息
     */
    public NextUnitInfoRespVO getCurrentUnitUnitInfo(
        @Valid AppInteractiveCourseUnitReqVO reqVO) {
        InteractiveCourseUnitDO currentUnit = interactiveCourseUnitService.getById(reqVO.getUnitId());
        if (currentUnit == null || Boolean.FALSE.equals(currentUnit.getDisplayStatus())) {
            return null;
        }

        Long currentUnitId = currentUnit.getId();
        NextUnitInfoRespVO respVO = new NextUnitInfoRespVO();

        // 查询下一个单元ID
        Long nextUnitId = interactiveCourseUnitService.getNextUnitId(currentUnit.getCourseId(), currentUnit.getSort());
        respVO.setNextUnitId(nextUnitId);

        respVO.setQuestionSource(currentUnit.getQuestionSource());
        respVO.setCurrentUnitId(currentUnitId);
        if (UnitQuestionSourceTypeEnum.SPECIAL_PRACTICE.getCode()
            .equals(currentUnit.getQuestionSource())) {
            // 专项练习：获取专项练习ID
            setSpecialExerciseInfo(currentUnitId, respVO);
        } else if (UnitQuestionSourceTypeEnum.QUESTION.getCode()
            .equals(currentUnit.getQuestionSource())) {
            // 真题练习：获取题目ID列表（只包含未禁用的题目）
            setQuestionInfo(currentUnitId, respVO);
        } else if (UnitQuestionSourceTypeEnum.VIDEO.getCode()
            .equals(currentUnit.getQuestionSource())) {
            // 视频：获取视频ID
            respVO.setVideoInfoId(currentUnit.getVideoId());
        }
        return respVO;
    }
}
