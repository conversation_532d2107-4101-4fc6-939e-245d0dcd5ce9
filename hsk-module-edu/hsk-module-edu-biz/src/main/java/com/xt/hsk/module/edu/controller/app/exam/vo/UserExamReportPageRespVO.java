package com.xt.hsk.module.edu.controller.app.exam.vo;

import com.xt.hsk.module.edu.enums.exam.ExamCorrectionStatusEnum;
import com.xt.hsk.module.edu.enums.exam.ExamRecordPracticeStatusEnum;
import com.xt.hsk.module.edu.enums.exam.ExamSubjectSectionsEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户模考报告页响应 VO
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
public class UserExamReportPageRespVO {

    /**
     * 总分
     */
    private Integer totalScore;

    /**
     * 得分
     */
    private Integer actualScore;

    /**
     * 总题数
     */
    private Integer questionNum;

    /**
     * 答题数
     */
    private Integer answeredNum;

    /**
     * 答对题数
     */
    private Integer correctNum;

    /**
     * 用时（秒）
     */
    private Integer answerTime;

    /**
     * 交卷时间
     */
    private LocalDateTime endTime;

    /**
     * 是否合格（得分≥总分*60%）
     */
    private Boolean qualified;

    /**
     * 模考id
     */
    private Long examId;

    /**
     * 模考类型：1-全真模考 2-30分钟模考 3-15分钟模考
     */
    private Integer examType;

    /**
     * 参与的模考科目 0-完整模考 1-听力 2-阅读 4-书写
     *
     * @see ExamSubjectSectionsEnum
     */
    private Integer examSections;

    /**
     * 批改状态 1进行中 2待批改 3已批改 4批改失败
     *
     * @see ExamCorrectionStatusEnum
     */
    private Integer correctionStatus;

    /**
     * 练习状态 1进行中 2已完成
     *
     * @see ExamRecordPracticeStatusEnum
     */
    private Integer practiceStatus;

    /**
     * 科目成绩列表
     */
    private List<UserExamSubjectScoreRespVO> subjectScoreList;
}