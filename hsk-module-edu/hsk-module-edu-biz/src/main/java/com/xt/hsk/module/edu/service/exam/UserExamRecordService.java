package com.xt.hsk.module.edu.service.exam;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.exam.vo.UserExamRecordPageReqVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.UserExamRecordPageRespVO;
import com.xt.hsk.module.edu.controller.app.exam.vo.ExamAppPageReqVO;
import com.xt.hsk.module.edu.controller.app.exam.vo.UserExamRecordAppRespVO;
import com.xt.hsk.module.edu.dal.dataobject.exam.UserExamRecordDO;

/**
 * 用户模考记录 Service 接口
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
public interface UserExamRecordService extends IService<UserExamRecordDO> {

    /**
     * 分页获取用户模考记录
     */
    PageResult<UserExamRecordPageRespVO> getUserExamRecordPage(UserExamRecordPageReqVO pageReqVO);

    /**
     * 用户模考记录总数
     */
    Long countUserExamRecordPage(UserExamRecordPageReqVO pageReqVO);

    PageResult<UserExamRecordAppRespVO> getMyExamRecordPage(ExamAppPageReqVO reqVO);
}