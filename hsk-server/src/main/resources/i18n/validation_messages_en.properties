# Common validation messages
javax.validation.constraints.NotBlank.message=cannot be blank
javax.validation.constraints.NotNull.message=cannot be null
javax.validation.constraints.NotEmpty.message=cannot be empty
javax.validation.constraints.Size.message=size must be between {min} and {max}
javax.validation.constraints.Min.message=must be greater than or equal to {value}
javax.validation.constraints.Max.message=must be less than or equal to {value}
javax.validation.constraints.Email.message=not a valid email address
javax.validation.constraints.Pattern.message=format is incorrect
# Custom validation messages

validation.id-card.invalid=ID card number format is incorrect
validation.password.weak=password strength is not enough
validation.username.invalid=username format is incorrect
# ============================ç»å½æ³¨åéªè¯æ¶æ¯================================
# å½å®¶åºå·éè¯¯
validation.invalid.country.code=Invalid country code
#ææºå·æ ¼å¼éè¯¯
validation.mobile.invalid=Invalid phone number format
validation.mobile.invalid_2=Please enter a valid phone number
#éªè¯ç å¿é¡»æ¯6ä½æ°å­
validation.verification.code.invalid=6-digit code required
#å¯ç æ ¼å¼ä¸æ­£ç¡®
validation.password.invalid=The password must be 8-16 characters long and must include letters and numbers.