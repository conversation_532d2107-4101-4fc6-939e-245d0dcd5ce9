<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xt.hsk.module.edu.dal.mysql.exam.UserExamRecordMapper">
    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <!-- 分页获取用户模考记录的条件 -->
    <sql id="selectPageConditions">
        <!-- 用户昵称 -->
        <if test="req.nickname != null and req.nickname != ''">
            AND u.nickname LIKE CONCAT('%', #{req.nickname}, '%')
        </if>

        <!-- 手机号码 -->
        <if test="req.mobile != null and req.mobile != ''">
            AND u.mobile LIKE CONCAT('%', #{req.mobile}, '%')
        </if>

        <!-- 记录状态 -->
        <if test="req.correctionStatus != null and req.correctionStatus != ''">
            AND r.correction_status LIKE CONCAT('%', #{req.correctionStatus}, '%')
        </if>

        <!-- 模考记录ID -->
        <if test="req.ids != null and req.ids.size() > 0">
            AND r.id IN
            <foreach item="id" collection="req.ids" separator="," close=")" index="index" open="(">
                #{id}
            </foreach>
        </if>
    </sql>

    <select id="getUserExamRecordPage"
            resultType="com.xt.hsk.module.edu.controller.admin.exam.vo.UserExamRecordPageRespVO">
        SELECT r.id,
        r.user_id,
        r.exam_id,
        r.hsk_level,
        r.exam_type,
        r.exam_sections,
        r.actual_score,
        r.listening_score,
        r.reading_score,
        r.writing_score,
        r.answer_time,
        r.start_time,
        r.end_time,
        r.correction_status,
        u.nickname,
        u.mobile,
        u.country_code
        FROM edu_user_exam_record r
        LEFT JOIN `user` u ON r.user_id = u.id
        WHERE r.deleted = 0
        AND r.exam_id = #{req.examId}
        AND r.exam_sections = #{req.examSections}
        AND r.exam_type = #{req.examType}
        <include refid="selectPageConditions"/>
        ORDER BY r.id DESC
    </select>

    <select id="countUserExamRecordPage" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM edu_user_exam_record r
        LEFT JOIN `user` u ON r.user_id = u.id
        WHERE r.deleted = 0
        AND r.exam_id = #{req.examId}
        AND r.exam_sections = #{req.examSections}
        AND r.exam_type = #{req.examType}
        <include refid="selectPageConditions"/>
    </select>
    <select id="getUserExamRecordCount" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM edu_user_exam_record r
        WHERE r.deleted = 0
          AND r.user_id = #{userId}
    </select>
        <select id="getMyExamRecordPage" resultType="com.xt.hsk.module.edu.controller.app.exam.vo.UserExamRecordAppRespVO">
        SELECT r.`id`,
               r.`id`   AS record_id,
               r.`exam_id`,
               r.`hsk_level`,
               r.`exam_type`,
               r.`exam_sections`,
               r.`total_score`,
               r.`actual_score`,
               r.`start_time`,
               r.`end_time`,
               r.`correction_status`,
               r.`progress`,
               r.`practice_status`,
               r.`current_sections`,
               e.`name` AS exam_name
        FROM edu_user_exam_record r
                 LEFT JOIN edu_exam e ON r.exam_id = e.id
        WHERE r.deleted = 0
          AND e.deleted = 0
          AND e.publish_status = 1
          and r.user_id = #{req.userId}
          and r.hsk_level = #{req.hskLevel}
        <if test="req.examId != null">
            and r.exam_id = #{req.examId}
        </if>
        <if test="req.practiceStatus != null">
            and r.practice_status = #{req.practiceStatus}
        </if>
        order by r.id desc

    </select>
</mapper>