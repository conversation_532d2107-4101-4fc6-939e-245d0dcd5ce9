package com.xt.hsk.module.thirdparty.enums;

import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.core.ArrayValuable;
import com.xt.hsk.framework.common.enums.BasicEnum;
import java.util.Arrays;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 视频清晰度枚举
 *
 * <AUTHOR>
 * @since 2025-08-02
 */
@Getter
@AllArgsConstructor
public enum VideoQualityEnum implements BasicEnum<Integer>, VO, ArrayValuable<Integer> {

    /**
     * 自适应
     */
    AD(1, "AD", "AD"),
    /**
     * 流畅
     */
    FLUENT(2, "FLUENT", "360P"),
    /**
     * 标清
     */
    SD(3, "SD", "720P"),
    /**
     * 高清
     */
    HD(4, "HD", "1080P"),
    /**
     * 超高清
     */
    FULL_HD(5, "FULL_HD", "1440P"),
    /**
     * 2K
     */
    TWO_K(6, "2K", "2K"),
    /**
     * 4K
     */
    FOUR_K(7, "4K", "4K"),
    ;

    private final Integer code;
    private final String desc;
    private final String resolution;

    private static final Integer[] ARRAYS = Arrays.stream(values())
            .map(VideoQualityEnum::getCode)
            .toArray(Integer[]::new);

    public static String getResolutionByCode(Integer code) {
        for (VideoQualityEnum value : values()) {
            if (value.code.equals(code)) {
                return value.resolution;
            }
        }
        return null;
    }

    @Override
    public Integer[] array() {
        return ARRAYS;
    }

}
